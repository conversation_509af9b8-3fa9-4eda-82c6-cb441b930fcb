-- Database Optimization: Enhanced Indexes and Performance Improvements
-- This migration adds comprehensive indexing for optimal query performance

-- ============================================================================
-- COMPOSITE INDEXES FOR COMMON QUERY PATTERNS
-- ============================================================================

-- Budget Categories: user_id + type for filtering by category type
CREATE INDEX IF NOT EXISTS idx_budget_categories_user_type 
ON public.budget_categories(user_id, type);

-- Budget Categories: user_id + name for searching by name
CREATE INDEX IF NOT EXISTS idx_budget_categories_user_name 
ON public.budget_categories(user_id, name);

-- Transactions: user_id + date for date range queries
CREATE INDEX IF NOT EXISTS idx_transactions_user_date 
ON public.transactions(user_id, date DESC);

-- Transactions: user_id + type + date for filtering by type and date
CREATE INDEX IF NOT EXISTS idx_transactions_user_type_date 
ON public.transactions(user_id, type, date DESC);

-- Transactions: category_id + date for category-specific queries
CREATE INDEX IF NOT EXISTS idx_transactions_category_date 
ON public.transactions(category_id, date DESC);

-- Tasks: user_id + completed for filtering completed/incomplete tasks
CREATE INDEX IF NOT EXISTS idx_tasks_user_completed 
ON public.tasks(user_id, completed);

-- Tasks: user_id + priority for priority-based queries
CREATE INDEX IF NOT EXISTS idx_tasks_user_priority 
ON public.tasks(user_id, priority);

-- Tasks: user_id + due_date for upcoming tasks
CREATE INDEX IF NOT EXISTS idx_tasks_user_due_date 
ON public.tasks(user_id, due_date) WHERE due_date IS NOT NULL;

-- Shopping Lists: user_id + is_shared for filtering shared lists
CREATE INDEX IF NOT EXISTS idx_shopping_lists_user_shared 
ON public.shopping_lists(user_id, is_shared);

-- Shopping List Items: shopping_list_id + completed for progress tracking
CREATE INDEX IF NOT EXISTS idx_shopping_list_items_list_completed 
ON public.shopping_list_items(shopping_list_id, completed);

-- Recipes: user_id + cuisine for cuisine-based filtering
CREATE INDEX IF NOT EXISTS idx_recipes_user_cuisine 
ON public.recipes(user_id, cuisine) WHERE cuisine IS NOT NULL;

-- Recipes: user_id + difficulty for difficulty-based filtering
CREATE INDEX IF NOT EXISTS idx_recipes_user_difficulty 
ON public.recipes(user_id, difficulty);

-- Chat Messages: conversation_id + created_at for message ordering
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_created 
ON public.chat_messages(conversation_id, created_at DESC);

-- ============================================================================
-- PARTIAL INDEXES FOR SPECIFIC CONDITIONS
-- ============================================================================

-- Active tasks only (not completed)
CREATE INDEX IF NOT EXISTS idx_tasks_active 
ON public.tasks(user_id, due_date, priority) 
WHERE completed = false;

-- Overdue tasks
CREATE INDEX IF NOT EXISTS idx_tasks_overdue 
ON public.tasks(user_id, due_date) 
WHERE completed = false AND due_date < CURRENT_DATE;

-- Recent transactions (last 30 days)
CREATE INDEX IF NOT EXISTS idx_transactions_recent 
ON public.transactions(user_id, date DESC) 
WHERE date >= CURRENT_DATE - INTERVAL '30 days';

-- Incomplete shopping list items
CREATE INDEX IF NOT EXISTS idx_shopping_items_incomplete 
ON public.shopping_list_items(shopping_list_id, created_at) 
WHERE completed = false;

-- ============================================================================
-- FULL-TEXT SEARCH INDEXES
-- ============================================================================

-- Tasks full-text search
CREATE INDEX IF NOT EXISTS idx_tasks_search 
ON public.tasks USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Recipes full-text search
CREATE INDEX IF NOT EXISTS idx_recipes_search 
ON public.recipes USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '') || ' ' || COALESCE(cuisine, '')));

-- Shopping list items search
CREATE INDEX IF NOT EXISTS idx_shopping_items_search 
ON public.shopping_list_items USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- ============================================================================
-- JSONB INDEXES FOR STRUCTURED DATA
-- ============================================================================

-- Recipe ingredients search
CREATE INDEX IF NOT EXISTS idx_recipes_ingredients 
ON public.recipes USING gin(ingredients);

-- Recipe instructions search
CREATE INDEX IF NOT EXISTS idx_recipes_instructions 
ON public.recipes USING gin(instructions);

-- Recipe tags search
CREATE INDEX IF NOT EXISTS idx_recipes_tags 
ON public.recipes USING gin(tags);

-- Task tags search
CREATE INDEX IF NOT EXISTS idx_tasks_tags 
ON public.tasks USING gin(tags);

-- ============================================================================
-- EXPRESSION INDEXES FOR COMPUTED VALUES
-- ============================================================================

-- Lowercase email search for profiles
CREATE INDEX IF NOT EXISTS idx_profiles_email_lower 
ON public.profiles(lower(email));

-- Month/year grouping for transactions
CREATE INDEX IF NOT EXISTS idx_transactions_month_year 
ON public.transactions(user_id, date_trunc('month', date));

-- ============================================================================
-- COVERING INDEXES FOR COMMON QUERIES
-- ============================================================================

-- Budget summary query optimization
CREATE INDEX IF NOT EXISTS idx_budget_categories_summary 
ON public.budget_categories(user_id) 
INCLUDE (name, type, budget_limit, color);

-- Transaction summary optimization
CREATE INDEX IF NOT EXISTS idx_transactions_summary 
ON public.transactions(user_id, date) 
INCLUDE (amount, type, category_id, description);

-- Task list optimization
CREATE INDEX IF NOT EXISTS idx_tasks_list 
ON public.tasks(user_id, completed, due_date) 
INCLUDE (title, priority, category);

-- ============================================================================
-- STATISTICS AND MAINTENANCE
-- ============================================================================

-- Update table statistics for better query planning
ANALYZE public.profiles;
ANALYZE public.tasks;
ANALYZE public.budget_categories;
ANALYZE public.transactions;
ANALYZE public.shopping_lists;
ANALYZE public.shopping_list_items;
ANALYZE public.recipes;
ANALYZE public.chat_conversations;
ANALYZE public.chat_messages;

-- ============================================================================
-- PERFORMANCE MONITORING VIEWS
-- ============================================================================

-- Create a view for monitoring slow queries
CREATE OR REPLACE VIEW public.slow_queries AS
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows,
  100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE mean_time > 100 -- queries taking more than 100ms on average
ORDER BY mean_time DESC;

-- Create a view for monitoring index usage
CREATE OR REPLACE VIEW public.index_usage AS
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_tup_read,
  idx_tup_fetch,
  idx_scan,
  CASE 
    WHEN idx_scan = 0 THEN 'Never used'
    WHEN idx_scan < 10 THEN 'Rarely used'
    WHEN idx_scan < 100 THEN 'Moderately used'
    ELSE 'Frequently used'
  END AS usage_level
FROM pg_stat_user_indexes
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Create a view for table size monitoring
CREATE OR REPLACE VIEW public.table_sizes AS
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS size,
  pg_total_relation_size(schemaname||'.'||tablename) AS size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_budget_categories_user_type IS 'Optimizes filtering budget categories by user and type';
COMMENT ON INDEX idx_transactions_user_date IS 'Optimizes transaction queries by user and date range';
COMMENT ON INDEX idx_tasks_user_completed IS 'Optimizes filtering tasks by completion status';
COMMENT ON INDEX idx_recipes_search IS 'Enables full-text search across recipe content';
COMMENT ON INDEX idx_tasks_active IS 'Partial index for active (incomplete) tasks only';
COMMENT ON VIEW slow_queries IS 'Monitors queries with high execution time';
COMMENT ON VIEW index_usage IS 'Tracks index usage statistics';
COMMENT ON VIEW table_sizes IS 'Monitors table storage sizes';
