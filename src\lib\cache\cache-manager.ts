/**
 * Comprehensive Caching System for LifeManager
 * Supports multiple cache strategies and storage backends
 */

import { securityConfig } from '@/lib/security/config'

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  tags?: string[] // Cache tags for invalidation
  namespace?: string // Cache namespace
  compress?: boolean // Compress large values
  serialize?: boolean // Custom serialization
}

export interface CacheEntry<T = any> {
  value: T
  createdAt: number
  expiresAt: number
  tags: string[]
  namespace: string
  size: number
}

export interface CacheStats {
  hits: number
  misses: number
  sets: number
  deletes: number
  size: number
  entries: number
}

// Cache storage interface
export interface CacheStorage {
  get<T>(key: string): Promise<CacheEntry<T> | null>
  set<T>(key: string, entry: CacheEntry<T>): Promise<void>
  delete(key: string): Promise<void>
  clear(): Promise<void>
  keys(pattern?: string): Promise<string[]>
  size(): Promise<number>
}

// In-memory cache storage
class MemoryCacheStorage implements CacheStorage {
  private cache = new Map<string, CacheEntry>()
  private maxSize: number
  private maxEntries: number

  constructor(maxSize = 100 * 1024 * 1024, maxEntries = 10000) { // 100MB, 10k entries
    this.maxSize = maxSize
    this.maxEntries = maxEntries
  }

  async get<T>(key: string): Promise<CacheEntry<T> | null> {
    const entry = this.cache.get(key)
    if (!entry) return null
    
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key)
      return null
    }
    
    return entry as CacheEntry<T>
  }

  async set<T>(key: string, entry: CacheEntry<T>): Promise<void> {
    // Check size limits
    if (this.cache.size >= this.maxEntries) {
      this.evictOldest()
    }
    
    const currentSize = this.getCurrentSize()
    if (currentSize + entry.size > this.maxSize) {
      this.evictBySize(entry.size)
    }
    
    this.cache.set(key, entry)
  }

  async delete(key: string): Promise<void> {
    this.cache.delete(key)
  }

  async clear(): Promise<void> {
    this.cache.clear()
  }

  async keys(pattern?: string): Promise<string[]> {
    const keys = Array.from(this.cache.keys())
    if (!pattern) return keys
    
    const regex = new RegExp(pattern.replace(/\*/g, '.*'))
    return keys.filter(key => regex.test(key))
  }

  async size(): Promise<number> {
    return this.getCurrentSize()
  }

  private getCurrentSize(): number {
    let size = 0
    for (const entry of this.cache.values()) {
      size += entry.size
    }
    return size
  }

  private evictOldest(): void {
    let oldestKey: string | null = null
    let oldestTime = Infinity
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
    }
  }

  private evictBySize(requiredSize: number): void {
    const entries = Array.from(this.cache.entries())
      .sort(([,a], [,b]) => a.createdAt - b.createdAt)
    
    let freedSize = 0
    for (const [key, entry] of entries) {
      this.cache.delete(key)
      freedSize += entry.size
      if (freedSize >= requiredSize) break
    }
  }
}

// Main cache manager
export class CacheManager {
  private storage: CacheStorage
  private stats: CacheStats
  private defaultTTL: number
  private enabled: boolean

  constructor(storage?: CacheStorage) {
    this.storage = storage || new MemoryCacheStorage()
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0,
      entries: 0
    }
    this.defaultTTL = securityConfig.validation.maxRequestSize || 3600 // 1 hour default
    this.enabled = process.env.ENABLE_CACHING !== 'false'
  }

  async get<T>(key: string, namespace = 'default'): Promise<T | null> {
    if (!this.enabled) return null
    
    const fullKey = this.buildKey(key, namespace)
    const entry = await this.storage.get<T>(fullKey)
    
    if (entry) {
      this.stats.hits++
      return entry.value
    }
    
    this.stats.misses++
    return null
  }

  async set<T>(
    key: string, 
    value: T, 
    options: CacheOptions = {}
  ): Promise<void> {
    if (!this.enabled) return
    
    const {
      ttl = this.defaultTTL,
      tags = [],
      namespace = 'default',
      compress = false,
      serialize = true
    } = options
    
    const fullKey = this.buildKey(key, namespace)
    const now = Date.now()
    
    let processedValue = value
    if (serialize && typeof value === 'object') {
      processedValue = JSON.parse(JSON.stringify(value)) as T
    }
    
    const entry: CacheEntry<T> = {
      value: processedValue,
      createdAt: now,
      expiresAt: now + (ttl * 1000),
      tags,
      namespace,
      size: this.calculateSize(processedValue)
    }
    
    await this.storage.set(fullKey, entry)
    this.stats.sets++
  }

  async delete(key: string, namespace = 'default'): Promise<void> {
    if (!this.enabled) return
    
    const fullKey = this.buildKey(key, namespace)
    await this.storage.delete(fullKey)
    this.stats.deletes++
  }

  async invalidateByTag(tag: string): Promise<void> {
    if (!this.enabled) return
    
    const keys = await this.storage.keys()
    const keysToDelete: string[] = []
    
    for (const key of keys) {
      const entry = await this.storage.get(key)
      if (entry && entry.tags.includes(tag)) {
        keysToDelete.push(key)
      }
    }
    
    await Promise.all(keysToDelete.map(key => this.storage.delete(key)))
  }

  async invalidateNamespace(namespace: string): Promise<void> {
    if (!this.enabled) return
    
    const pattern = `${namespace}:*`
    const keys = await this.storage.keys(pattern)
    await Promise.all(keys.map(key => this.storage.delete(key)))
  }

  async clear(): Promise<void> {
    if (!this.enabled) return
    
    await this.storage.clear()
    this.resetStats()
  }

  getStats(): CacheStats {
    return { ...this.stats }
  }

  private buildKey(key: string, namespace: string): string {
    return `${namespace}:${key}`
  }

  private calculateSize(value: any): number {
    if (typeof value === 'string') {
      return value.length * 2 // UTF-16
    }
    if (typeof value === 'object') {
      return JSON.stringify(value).length * 2
    }
    return 8 // Approximate for primitives
  }

  private resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      size: 0,
      entries: 0
    }
  }
}

// Global cache instance
export const cacheManager = new CacheManager()

// Cache decorators and utilities
export function cached<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  options: CacheOptions & { keyGenerator?: (...args: Parameters<T>) => string } = {}
): T {
  const { keyGenerator, ...cacheOptions } = options
  
  return (async (...args: Parameters<T>) => {
    const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)
    
    // Try to get from cache
    const cached = await cacheManager.get(key, cacheOptions.namespace)
    if (cached !== null) {
      return cached
    }
    
    // Execute function and cache result
    const result = await fn(...args)
    await cacheManager.set(key, result, cacheOptions)
    
    return result
  }) as T
}

// Database query caching helper
export async function cacheQuery<T>(
  queryKey: string,
  queryFn: () => Promise<T>,
  options: CacheOptions = {}
): Promise<T> {
  const cached = await cacheManager.get<T>(queryKey, 'database')
  if (cached !== null) {
    return cached
  }
  
  const result = await queryFn()
  await cacheManager.set(queryKey, result, {
    ...options,
    namespace: 'database',
    tags: ['database', ...(options.tags || [])]
  })
  
  return result
}
