import { createClient } from '@/lib/supabase/client'

export interface TripCollaborator {
  id: string
  trip_id: string
  user_id: string
  invited_by: string
  permission_level: 'view' | 'edit' | 'admin'
  status: 'pending' | 'accepted' | 'declined'
  invited_at: string
  responded_at?: string
  user?: {
    id: string
    email: string
    full_name?: string
    avatar_url?: string
  }
}

export interface TripInvitation {
  id: string
  trip_id: string
  invited_by: string
  email: string
  permission_level: 'view' | 'edit' | 'admin'
  invitation_token: string
  status: 'pending' | 'accepted' | 'declined' | 'expired'
  expires_at: string
  created_at: string
}

export interface TripActivity {
  id: string
  trip_id: string
  user_id?: string
  action_type: string
  entity_type?: string
  entity_id?: string
  description?: string
  metadata: any
  created_at: string
  user?: {
    id: string
    email: string
    full_name?: string
    avatar_url?: string
  }
}

export interface ShareTripOptions {
  permission_level: 'view' | 'edit' | 'admin'
  share_via_link?: boolean
}

class TripCollaborationService {
  private supabase = createClient()

  async shareTrip(tripId: string, options: ShareTripOptions): Promise<{ share_code?: string }> {
    const { data: trip, error: tripError } = await this.supabase
      .from('trips')
      .update({
        is_shared: true,
        share_permissions: options.permission_level
      })
      .eq('id', tripId)
      .select('share_code')
      .single()

    if (tripError) throw tripError

    // Log activity
    await this.logActivity(tripId, 'shared', 'trip', tripId, 'Trip shared with collaborators')

    return { share_code: trip.share_code }
  }

  async inviteCollaborator(
    tripId: string, 
    email: string, 
    permissionLevel: 'view' | 'edit' | 'admin' = 'view'
  ): Promise<TripInvitation> {
    // Check if user exists
    const { data: existingUser } = await this.supabase
      .from('profiles')
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      // Add as collaborator directly
      const { data: collaborator, error: collabError } = await this.supabase
        .from('trip_collaborators')
        .insert({
          trip_id: tripId,
          user_id: existingUser.id,
          permission_level: permissionLevel,
          status: 'pending'
        })
        .select()
        .single()

      if (collabError) throw collabError

      // Log activity
      await this.logActivity(tripId, 'invited', 'collaborator', collaborator.id, `Invited ${email} to collaborate`)

      // Return a mock invitation for consistency
      return {
        id: collaborator.id,
        trip_id: tripId,
        invited_by: collaborator.invited_by,
        email,
        permission_level: permissionLevel,
        invitation_token: '',
        status: 'pending',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: collaborator.created_at
      }
    } else {
      // Create email invitation
      const invitationToken = this.generateInvitationToken()
      
      const { data: invitation, error: inviteError } = await this.supabase
        .from('trip_invitations')
        .insert({
          trip_id: tripId,
          email,
          permission_level: permissionLevel,
          invitation_token: invitationToken
        })
        .select()
        .single()

      if (inviteError) throw inviteError

      // Log activity
      await this.logActivity(tripId, 'invited', 'invitation', invitation.id, `Sent invitation to ${email}`)

      return invitation
    }
  }

  async acceptInvitation(invitationId: string): Promise<TripCollaborator> {
    const { data: invitation, error: inviteError } = await this.supabase
      .from('trip_invitations')
      .select('*')
      .eq('id', invitationId)
      .single()

    if (inviteError) throw inviteError

    // Update invitation status
    await this.supabase
      .from('trip_invitations')
      .update({ status: 'accepted', responded_at: new Date().toISOString() })
      .eq('id', invitationId)

    // Add as collaborator
    const { data: collaborator, error: collabError } = await this.supabase
      .from('trip_collaborators')
      .insert({
        trip_id: invitation.trip_id,
        permission_level: invitation.permission_level,
        status: 'accepted'
      })
      .select()
      .single()

    if (collabError) throw collabError

    // Log activity
    await this.logActivity(invitation.trip_id, 'joined', 'trip', invitation.trip_id, 'Joined trip as collaborator')

    return collaborator
  }

  async joinTripByShareCode(shareCode: string): Promise<TripCollaborator> {
    // Find trip by share code
    const { data: trip, error: tripError } = await this.supabase
      .from('trips')
      .select('id, share_permissions')
      .eq('share_code', shareCode)
      .eq('is_shared', true)
      .single()

    if (tripError) throw tripError

    // Add as collaborator
    const { data: collaborator, error: collabError } = await this.supabase
      .from('trip_collaborators')
      .insert({
        trip_id: trip.id,
        permission_level: trip.share_permissions,
        status: 'accepted'
      })
      .select()
      .single()

    if (collabError) throw collabError

    // Log activity
    await this.logActivity(trip.id, 'joined', 'trip', trip.id, 'Joined trip via share link')

    return collaborator
  }

  async getCollaborators(tripId: string): Promise<TripCollaborator[]> {
    const { data, error } = await this.supabase
      .from('trip_collaborators')
      .select(`
        *,
        user:profiles(id, email, full_name, avatar_url)
      `)
      .eq('trip_id', tripId)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  }

  async updateCollaboratorPermission(
    collaboratorId: string, 
    permissionLevel: 'view' | 'edit' | 'admin'
  ): Promise<void> {
    const { error } = await this.supabase
      .from('trip_collaborators')
      .update({ permission_level: permissionLevel })
      .eq('id', collaboratorId)

    if (error) throw error
  }

  async removeCollaborator(collaboratorId: string): Promise<void> {
    const { data: collaborator, error: getError } = await this.supabase
      .from('trip_collaborators')
      .select('trip_id, user:profiles(email)')
      .eq('id', collaboratorId)
      .single()

    if (getError) throw getError

    const { error } = await this.supabase
      .from('trip_collaborators')
      .delete()
      .eq('id', collaboratorId)

    if (error) throw error

    // Log activity
    await this.logActivity(
      collaborator.trip_id, 
      'removed', 
      'collaborator', 
      collaboratorId, 
      `Removed ${collaborator.user?.email} from trip`
    )
  }

  async leaveTrip(tripId: string): Promise<void> {
    const { error } = await this.supabase
      .from('trip_collaborators')
      .delete()
      .eq('trip_id', tripId)
      .eq('user_id', (await this.supabase.auth.getUser()).data.user?.id)

    if (error) throw error

    // Log activity
    await this.logActivity(tripId, 'left', 'trip', tripId, 'Left trip')
  }

  async getTripActivity(tripId: string, limit: number = 50): Promise<TripActivity[]> {
    const { data, error } = await this.supabase
      .from('trip_activity_log')
      .select(`
        *,
        user:profiles(id, email, full_name, avatar_url)
      `)
      .eq('trip_id', tripId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) throw error
    return data || []
  }

  async logActivity(
    tripId: string,
    actionType: string,
    entityType?: string,
    entityId?: string,
    description?: string,
    metadata: any = {}
  ): Promise<void> {
    const user = (await this.supabase.auth.getUser()).data.user
    
    const { error } = await this.supabase
      .from('trip_activity_log')
      .insert({
        trip_id: tripId,
        user_id: user?.id,
        action_type: actionType,
        entity_type: entityType,
        entity_id: entityId,
        description,
        metadata
      })

    if (error) console.error('Failed to log activity:', error)
  }

  private generateInvitationToken(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  }

  async checkUserPermission(tripId: string, userId?: string): Promise<'owner' | 'admin' | 'edit' | 'view' | null> {
    const currentUserId = userId || (await this.supabase.auth.getUser()).data.user?.id
    if (!currentUserId) return null

    // Check if user is owner
    const { data: trip } = await this.supabase
      .from('trips')
      .select('user_id')
      .eq('id', tripId)
      .single()

    if (trip?.user_id === currentUserId) return 'owner'

    // Check collaborator permission
    const { data: collaborator } = await this.supabase
      .from('trip_collaborators')
      .select('permission_level')
      .eq('trip_id', tripId)
      .eq('user_id', currentUserId)
      .eq('status', 'accepted')
      .single()

    return collaborator?.permission_level || null
  }
}

export const tripCollaborationService = new TripCollaborationService()
