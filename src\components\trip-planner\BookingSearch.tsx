'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { 
  SearchIcon, 
  CalendarIcon, 
  UsersIcon,
  DollarSignIcon,
  StarIcon,
  ExternalLinkIcon,
  MapPinIcon,
  BedIcon,
  PlaneIcon,
  CarIcon,
  CameraIcon,
  UtensilsIcon
} from 'lucide-react'
import { bookingService, BookingIntegration, type BookingSearchParams, type BookingOption } from '@/lib/services/booking-service'
import toast from 'react-hot-toast'

interface BookingSearchProps {
  tripId: string
  destination: {
    city?: string
    country: string
    latitude?: number
    longitude?: number
  }
  dates: {
    start_date: Date
    end_date: Date
  }
  className?: string
}

export default function BookingSearch({ tripId, destination, dates, className = '' }: BookingSearchProps) {
  const [searchType, setSearchType] = useState<'accommodation' | 'flight' | 'car_rental' | 'activity' | 'restaurant'>('accommodation')
  const [searchResults, setSearchResults] = useState<BookingOption[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [searchParams, setSearchParams] = useState<Partial<BookingSearchParams>>({
    guests: { adults: 2, children: 0, rooms: 1 },
    budget: { min: 50, max: 500, currency: 'USD' }
  })

  const handleSearch = async () => {
    if (!destination.latitude || !destination.longitude) {
      toast.error('Location coordinates are required for booking search')
      return
    }

    setIsSearching(true)
    try {
      const params: BookingSearchParams = {
        type: searchType,
        location: {
          latitude: destination.latitude,
          longitude: destination.longitude,
          city: destination.city,
          country: destination.country
        },
        dates: {
          check_in: dates.start_date,
          check_out: dates.end_date
        },
        guests: searchParams.guests,
        budget: searchParams.budget
      }

      const results = await bookingService.searchBookingOptions(params)
      setSearchResults(results)
      toast.success(`Found ${results.length} booking options`)
    } catch (error) {
      console.error('Search failed:', error)
      toast.error('Failed to search booking options')
    } finally {
      setIsSearching(false)
    }
  }

  const handleBookingClick = (option: BookingOption) => {
    BookingIntegration.trackBookingClick(option.provider_id, option.id, tripId)
    
    const bookingUrl = BookingIntegration.generateAffiliateLink(
      option.provider_id,
      option.booking_url
    )
    
    window.open(bookingUrl, '_blank')
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'accommodation': return <BedIcon className="h-4 w-4" />
      case 'flight': return <PlaneIcon className="h-4 w-4" />
      case 'car_rental': return <CarIcon className="h-4 w-4" />
      case 'activity': return <CameraIcon className="h-4 w-4" />
      case 'restaurant': return <UtensilsIcon className="h-4 w-4" />
      default: return <SearchIcon className="h-4 w-4" />
    }
  }

  const formatPrice = (price: { amount: number; currency: string; per_night?: boolean }) => {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: price.currency
    }).format(price.amount)
    
    return price.per_night ? `${formatted}/night` : formatted
  }

  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 >= 0.5

    for (let i = 0; i < fullStars; i++) {
      stars.push(<StarIcon key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />)
    }
    
    if (hasHalfStar) {
      stars.push(<StarIcon key="half" className="h-3 w-3 fill-yellow-400 text-yellow-400 opacity-50" />)
    }
    
    const emptyStars = 5 - Math.ceil(rating)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<StarIcon key={`empty-${i}`} className="h-3 w-3 text-gray-300" />)
    }
    
    return stars
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <SearchIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Booking Search</h3>
            <Badge variant="secondary">{destination.city}, {destination.country}</Badge>
          </div>
        </div>

        {/* Search Controls */}
        <div className="space-y-4">
          {/* Booking Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              What are you looking for?
            </label>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'accommodation', label: 'Hotels & Stays', icon: <BedIcon className="h-4 w-4" /> },
                { value: 'flight', label: 'Flights', icon: <PlaneIcon className="h-4 w-4" /> },
                { value: 'car_rental', label: 'Car Rental', icon: <CarIcon className="h-4 w-4" /> },
                { value: 'activity', label: 'Activities', icon: <CameraIcon className="h-4 w-4" /> },
                { value: 'restaurant', label: 'Restaurants', icon: <UtensilsIcon className="h-4 w-4" /> }
              ].map((type) => (
                <button
                  key={type.value}
                  onClick={() => setSearchType(type.value as any)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg border transition-colors ${
                    searchType === type.value
                      ? 'bg-blue-50 border-blue-300 text-blue-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  {type.icon}
                  <span className="text-sm">{type.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Guests and Budget */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Guests
              </label>
              <div className="flex space-x-2">
                <Input
                  type="number"
                  min="1"
                  max="20"
                  value={searchParams.guests?.adults || 2}
                  onChange={(e) => setSearchParams(prev => ({
                    ...prev,
                    guests: { ...prev.guests!, adults: parseInt(e.target.value) }
                  }))}
                  placeholder="Adults"
                />
                <Input
                  type="number"
                  min="0"
                  max="10"
                  value={searchParams.guests?.children || 0}
                  onChange={(e) => setSearchParams(prev => ({
                    ...prev,
                    guests: { ...prev.guests!, children: parseInt(e.target.value) }
                  }))}
                  placeholder="Children"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Budget Range
              </label>
              <div className="flex space-x-2">
                <Input
                  type="number"
                  value={searchParams.budget?.min || 50}
                  onChange={(e) => setSearchParams(prev => ({
                    ...prev,
                    budget: { ...prev.budget!, min: parseInt(e.target.value) }
                  }))}
                  placeholder="Min"
                />
                <Input
                  type="number"
                  value={searchParams.budget?.max || 500}
                  onChange={(e) => setSearchParams(prev => ({
                    ...prev,
                    budget: { ...prev.budget!, max: parseInt(e.target.value) }
                  }))}
                  placeholder="Max"
                />
              </div>
            </div>

            <div className="flex items-end">
              <Button
                onClick={handleSearch}
                disabled={isSearching}
                className="w-full"
              >
                {isSearching ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                ) : (
                  <SearchIcon className="h-4 w-4 mr-2" />
                )}
                {isSearching ? 'Searching...' : 'Search'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <div className="p-4">
          <h4 className="font-medium text-gray-900 mb-4">
            Found {searchResults.length} options
          </h4>
          <div className="space-y-4">
            {searchResults.map((option) => (
              <div key={option.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {getTypeIcon(option.type)}
                      <h5 className="font-semibold text-gray-900">{option.name}</h5>
                      {option.rating && (
                        <div className="flex items-center space-x-1">
                          <div className="flex">{renderStars(option.rating.score)}</div>
                          <span className="text-sm text-gray-600">
                            ({option.rating.reviews_count} reviews)
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{option.description}</p>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                      <div className="flex items-center space-x-1">
                        <MapPinIcon className="h-3 w-3" />
                        <span>{option.location.address}</span>
                      </div>
                    </div>

                    {option.amenities.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {option.amenities.slice(0, 4).map((amenity) => (
                          <Badge key={amenity} variant="secondary" className="text-xs">
                            {amenity}
                          </Badge>
                        ))}
                        {option.amenities.length > 4 && (
                          <Badge variant="secondary" className="text-xs">
                            +{option.amenities.length - 4} more
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="text-right ml-4">
                    <div className="text-lg font-semibold text-gray-900 mb-1">
                      {formatPrice(option.price)}
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      {option.availability.available ? (
                        <span className="text-green-600">Available</span>
                      ) : (
                        <span className="text-red-600">Not Available</span>
                      )}
                    </div>
                    <Button
                      onClick={() => handleBookingClick(option)}
                      disabled={!option.availability.available}
                      size="sm"
                    >
                      <ExternalLinkIcon className="h-4 w-4 mr-1" />
                      Book Now
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Empty State */}
      {searchResults.length === 0 && !isSearching && (
        <div className="p-8 text-center text-gray-500">
          <SearchIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>Search for booking options to see results here</p>
          <p className="text-sm mt-1">
            {BookingIntegration.getBookingInstructions('booking_com')}
          </p>
        </div>
      )}
    </div>
  )
}
