# LifeManager Environment Configuration

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Security Mode: 'strict', 'relaxed', or leave empty for environment default
# - strict: Production-level security (recommended for production)
# - relaxed: Development-friendly security (recommended for development)
# - empty: Uses NODE_ENV to determine (production = strict, development = relaxed)
SECURITY_MODE=

# Individual Security Feature Toggles
# Set to 'true' to disable specific security features (use with caution)
DISABLE_RATE_LIMITING=false
DISABLE_CSRF=false
DISABLE_EMAIL_VERIFICATION=false

# Enable debug logging (not recommended for production)
ENABLE_DEBUG_LOGGING=false

# CORS Configuration
# Comma-separated list of allowed origins
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Allowed origins for production (override CORS_ORIGINS in production)
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# =============================================================================
# EXTERNAL SERVICES
# =============================================================================

# OpenAI Configuration (for AI chat features)
OPENAI_API_KEY=your_openai_api_key

# Spoonacular API (for recipe import and nutritional analysis)
SPOONACULAR_API_KEY=your_spoonacular_api_key

# Google Maps API (for trip planning)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# Next.js Configuration
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000

# Session timeout in milliseconds (default: 8 hours)
SESSION_TIMEOUT=********

# Password requirements
PASSWORD_MIN_LENGTH=12
REQUIRE_STRONG_PASSWORD=true

# Multi-factor authentication
ENABLE_MFA=false

# Account lockout settings
MAX_FAILED_ATTEMPTS=5
LOCKOUT_DURATION=1800000

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================

# Custom rate limits (optional - overrides defaults)
# Format: number of requests per time window in milliseconds

# Authentication endpoints
AUTH_RATE_LIMIT_REQUESTS=5
AUTH_RATE_LIMIT_WINDOW=900000

# API endpoints
API_RATE_LIMIT_REQUESTS=100
API_RATE_LIMIT_WINDOW=3600000

# Strict endpoints (sensitive operations)
STRICT_RATE_LIMIT_REQUESTS=20
STRICT_RATE_LIMIT_WINDOW=3600000

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================

# Log level: 'debug', 'info', 'warn', 'error'
LOG_LEVEL=info

# Enable audit logging
ENABLE_AUDIT_LOG=true

# Enable security event logging
ENABLE_SECURITY_EVENTS=true

# Optional: Analytics and Monitoring
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id

# Optional: Error Tracking
SENTRY_DSN=your_sentry_dsn

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Maximum request size in bytes (default: 5MB)
MAX_REQUEST_SIZE=5242880

# Enable response caching
ENABLE_CACHING=true

# Cache TTL in seconds
CACHE_TTL=3600

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
NODE_ENV=development

# Database URL for local development (if using local Supabase)
DATABASE_URL=postgresql://postgres:password@localhost:54322/postgres

# Redis URL for rate limiting (optional - uses in-memory by default)
REDIS_URL=redis://localhost:6379

# Development helpers
DEV_SKIP_EMAIL_VERIFICATION=true
DEV_AUTO_LOGIN=false
DEV_MOCK_EXTERNAL_SERVICES=false
DEV_ENABLE_TOOLS=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Application URL (used for redirects and email links)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# CDN URL for static assets (optional)
NEXT_PUBLIC_CDN_URL=

# Email service configuration
EMAIL_SERVICE_API_KEY=your_email_service_key
EMAIL_FROM_ADDRESS=<EMAIL>

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Enable/disable specific features
ENABLE_AI_CHAT=true
ENABLE_TRIP_PLANNER=true
ENABLE_RECIPE_IMPORT=true
ENABLE_BUDGET_TRACKING=true
ENABLE_SHOPPING_LISTS=true
ENABLE_TASK_MANAGEMENT=true

# =============================================================================
# SECURITY HEADERS CONFIGURATION
# =============================================================================

# Content Security Policy (CSP) - set to 'false' to disable
ENABLE_CSP=true

# HTTP Strict Transport Security (HSTS) - automatically enabled in production
ENABLE_HSTS=true

# X-Frame-Options header
ENABLE_X_FRAME_OPTIONS=true

# X-Content-Type-Options header
ENABLE_X_CONTENT_TYPE_OPTIONS=true

# Referrer Policy header
ENABLE_REFERRER_POLICY=true
