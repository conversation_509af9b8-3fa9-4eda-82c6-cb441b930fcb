{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/trip-planner-service.ts"], "sourcesContent": ["export interface Trip {\n  id: string\n  user_id: string\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  status: 'planning' | 'booked' | 'in_progress' | 'completed' | 'cancelled'\n  budget_total?: number\n  budget_spent: number\n  currency: string\n  traveler_count: number\n  is_shared: boolean\n  cover_image_url?: string\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_destinations?: TripDestination[]\n}\n\nexport interface TripDestination {\n  id: string\n  trip_id: string\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n  trip_activities?: TripActivity[]\n}\n\nexport interface TripActivity {\n  id: string\n  trip_id: string\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  status: 'planned' | 'booked' | 'completed' | 'cancelled'\n  priority: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripExpense {\n  id: string\n  trip_id: string\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency: string\n  exchange_rate: number\n  amount_usd: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense: boolean\n  shared_with_count: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TripTransport {\n  id: string\n  trip_id: string\n  from_destination_id?: string\n  to_destination_id?: string\n  transport_type: 'flight' | 'train' | 'bus' | 'car_rental' | 'taxi' | 'rideshare' | 'ferry' | 'walking' | 'cycling' | 'other'\n  provider_name?: string\n  departure_datetime?: string\n  arrival_datetime?: string\n  duration_minutes?: number\n  distance_km?: number\n  cost?: number\n  currency: string\n  booking_reference?: string\n  booking_url?: string\n  from_location?: string\n  to_location?: string\n  from_latitude?: number\n  from_longitude?: number\n  to_latitude?: number\n  to_longitude?: number\n  fuel_cost?: number\n  fuel_consumption_liters?: number\n  toll_costs?: number\n  parking_costs?: number\n  notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface CreateTripData {\n  title: string\n  description?: string\n  destination_country: string\n  destination_city?: string\n  start_date: string\n  end_date: string\n  trip_type?: 'leisure' | 'business' | 'adventure' | 'family' | 'romantic' | 'solo' | 'group'\n  budget_total?: number\n  currency?: string\n  traveler_count?: number\n  cover_image_url?: string\n  notes?: string\n}\n\nexport interface UpdateTripData extends Partial<CreateTripData> {}\n\nexport interface CreateDestinationData {\n  name: string\n  country: string\n  city?: string\n  latitude?: number\n  longitude?: number\n  arrival_date?: string\n  departure_date?: string\n  order_index?: number\n  accommodation_name?: string\n  accommodation_address?: string\n  accommodation_cost?: number\n  notes?: string\n}\n\nexport interface CreateActivityData {\n  destination_id?: string\n  title: string\n  description?: string\n  category: 'sightseeing' | 'dining' | 'entertainment' | 'shopping' | 'transport' | 'accommodation' | 'adventure' | 'cultural' | 'relaxation' | 'business'\n  scheduled_date?: string\n  scheduled_time?: string\n  duration_minutes?: number\n  cost?: number\n  currency?: string\n  location_name?: string\n  location_address?: string\n  latitude?: number\n  longitude?: number\n  booking_reference?: string\n  booking_url?: string\n  priority?: 'low' | 'medium' | 'high' | 'must_do'\n  notes?: string\n}\n\nexport interface CreateExpenseData {\n  destination_id?: string\n  activity_id?: string\n  category: 'accommodation' | 'transport' | 'food' | 'activities' | 'shopping' | 'fuel' | 'parking' | 'tolls' | 'insurance' | 'visas' | 'other'\n  subcategory?: string\n  description: string\n  amount: number\n  currency?: string\n  exchange_rate?: number\n  expense_date: string\n  payment_method?: string\n  receipt_url?: string\n  is_shared_expense?: boolean\n  shared_with_count?: number\n  notes?: string\n}\n\nclass TripPlannerService {\n  private baseUrl = '/api/trips'\n\n  // Trip CRUD operations\n  async getTrips(): Promise<Trip[]> {\n    const response = await fetch(this.baseUrl, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trips: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trips || []\n  }\n\n  async getTrip(id: string): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch trip: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.trip\n  }\n\n  async createTrip(data: CreateTripData): Promise<Trip> {\n    const response = await fetch(this.baseUrl, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to create trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async updateTrip(id: string, data: UpdateTripData): Promise<Trip> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to update trip: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.trip\n  }\n\n  async deleteTrip(id: string): Promise<void> {\n    const response = await fetch(`${this.baseUrl}/${id}`, {\n      method: 'DELETE',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to delete trip: ${response.statusText}`)\n    }\n  }\n\n  // Destination operations\n  async getDestinations(tripId: string): Promise<TripDestination[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch destinations: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.destinations || []\n  }\n\n  async addDestination(tripId: string, data: CreateDestinationData): Promise<TripDestination> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/destinations`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add destination: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.destination\n  }\n\n  // Activity operations\n  async getActivities(tripId: string): Promise<TripActivity[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch activities: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.activities || []\n  }\n\n  async addActivity(tripId: string, data: CreateActivityData): Promise<TripActivity> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/activities`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add activity: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.activity\n  }\n\n  // Expense operations\n  async getExpenses(tripId: string): Promise<TripExpense[]> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'GET',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to fetch expenses: ${response.statusText}`)\n    }\n\n    const data = await response.json()\n    return data.expenses || []\n  }\n\n  async addExpense(tripId: string, data: CreateExpenseData): Promise<TripExpense> {\n    const response = await fetch(`${this.baseUrl}/${tripId}/expenses`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(data),\n    })\n\n    if (!response.ok) {\n      throw new Error(`Failed to add expense: ${response.statusText}`)\n    }\n\n    const result = await response.json()\n    return result.expense\n  }\n\n  // Utility methods\n  calculateTripDuration(startDate: string, endDate: string): number {\n    const start = new Date(startDate)\n    const end = new Date(endDate)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  calculateBudgetProgress(budgetTotal?: number, budgetSpent?: number): number {\n    if (!budgetTotal || budgetTotal === 0) return 0\n    return Math.min((budgetSpent || 0) / budgetTotal * 100, 100)\n  }\n\n  formatCurrency(amount: number, currency: string = 'USD'): string {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: currency,\n    }).format(amount)\n  }\n}\n\nexport const tripPlannerService = new TripPlannerService()\n"], "names": [], "mappings": ";;;AA6LA,MAAM;IACI,UAAU,aAAY;IAE9B,uBAAuB;IACvB,MAAM,WAA4B;QAChC,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,KAAK,IAAI,EAAE;IACzB;IAEA,MAAM,QAAQ,EAAU,EAAiB;QACvC,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAChE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,IAAI;IAClB;IAEA,MAAM,WAAW,IAAoB,EAAiB;QACpD,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;YACzC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAE,IAAoB,EAAiB;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,WAAW,EAAU,EAAiB;QAC1C,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,MAAc,EAA8B;QAChE,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI,EAAE;IAChC;IAEA,MAAM,eAAe,MAAc,EAAE,IAA2B,EAA4B;QAC1F,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,SAAS,UAAU,EAAE;QACrE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,WAAW;IAC3B;IAEA,sBAAsB;IACtB,MAAM,cAAc,MAAc,EAA2B;QAC3D,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,SAAS,UAAU,EAAE;QACtE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,IAAI,EAAE;IAC9B;IAEA,MAAM,YAAY,MAAc,EAAE,IAAwB,EAAyB;QACjF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,WAAW,CAAC,EAAE;YACnE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,SAAS,UAAU,EAAE;QAClE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,QAAQ;IACxB;IAEA,qBAAqB;IACrB,MAAM,YAAY,MAAc,EAA0B;QACxD,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,QAAQ,IAAI,EAAE;IAC5B;IAEA,MAAM,WAAW,MAAc,EAAE,IAAuB,EAAwB;QAC9E,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,OAAO,SAAS,CAAC,EAAE;YACjE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,SAAS,UAAU,EAAE;QACjE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO,OAAO,OAAO;IACvB;IAEA,kBAAkB;IAClB,sBAAsB,SAAiB,EAAE,OAAe,EAAU;QAChE,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,MAAM,IAAI,KAAK;QACrB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,wBAAwB,WAAoB,EAAE,WAAoB,EAAU;QAC1E,IAAI,CAAC,eAAe,gBAAgB,GAAG,OAAO;QAC9C,OAAO,KAAK,GAAG,CAAC,CAAC,eAAe,CAAC,IAAI,cAAc,KAAK;IAC1D;IAEA,eAAe,MAAc,EAAE,WAAmB,KAAK,EAAU;QAC/D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;AACF;AAEO,MAAM,qBAAqB,IAAI", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\nimport { cva, type VariantProps } from 'class-variance-authority'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none disabled:cursor-not-allowed ring-offset-background transform hover:scale-105 active:scale-95 focus:scale-105',\n  {\n    variants: {\n      variant: {\n        default: 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl focus-visible:ring-blue-500',\n        destructive: 'bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 shadow-lg hover:shadow-xl focus-visible:ring-red-500',\n        outline: 'border-2 border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus-visible:ring-gray-500',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-900 hover:from-gray-200 hover:to-gray-300 focus-visible:ring-gray-500',\n        ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',\n        link: 'underline-offset-4 hover:underline text-blue-600 focus-visible:ring-blue-500',\n        success: 'bg-gradient-to-r from-green-600 to-green-700 text-white hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl focus-visible:ring-green-500',\n        warning: 'bg-gradient-to-r from-yellow-500 to-yellow-600 text-white hover:from-yellow-600 hover:to-yellow-700 shadow-lg hover:shadow-xl focus-visible:ring-yellow-500',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 text-xs',\n        lg: 'h-12 px-8 text-base',\n        xl: 'h-14 px-10 text-lg',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean\n  loadingText?: string\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, loadingText, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        aria-disabled={disabled || loading}\n        {...props}\n      >\n        {loading ? (\n          <>\n            <svg\n              className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              aria-hidden=\"true\"\n            >\n              <circle\n                className=\"opacity-25\"\n                cx=\"12\"\n                cy=\"12\"\n                r=\"10\"\n                stroke=\"currentColor\"\n                strokeWidth=\"4\"\n              ></circle>\n              <path\n                className=\"opacity-75\"\n                fill=\"currentColor\"\n                d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n              ></path>\n            </svg>\n            {loadingText || 'Loading...'}\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,gVACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;QACX;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACtB,iBAAe,YAAY;QAC1B,GAAG,KAAK;kBAER,wBACC;;8BACE,8OAAC;oBACC,WAAU;oBACV,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,eAAY;;sCAEZ,8OAAC;4BACC,WAAU;4BACV,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,QAAO;4BACP,aAAY;;;;;;sCAEd,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,GAAE;;;;;;;;;;;;gBAGL,eAAe;;2BAGlB;;;;;;AAIR;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Loading.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse'\n  className?: string\n  text?: string\n}\n\nconst sizeClasses = {\n  sm: 'h-4 w-4',\n  md: 'h-6 w-6',\n  lg: 'h-8 w-8',\n  xl: 'h-12 w-12',\n}\n\nexport function Loading({ \n  size = 'md', \n  variant = 'spinner', \n  className,\n  text \n}: LoadingProps) {\n  if (variant === 'spinner') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'dots') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div className=\"flex space-x-1\">\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]\"></div>\n          <div className=\"h-2 w-2 bg-blue-600 rounded-full animate-bounce\"></div>\n        </div>\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  if (variant === 'pulse') {\n    return (\n      <div className={cn('flex flex-col items-center justify-center space-y-2', className)}>\n        <div\n          className={cn(\n            'bg-blue-600 rounded-full animate-pulse',\n            sizeClasses[size]\n          )}\n        />\n        {text && <p className=\"text-sm text-gray-600\">{text}</p>}\n      </div>\n    )\n  }\n\n  return null\n}\n\nexport function LoadingSkeleton({ className }: { className?: string }) {\n  return (\n    <div className={cn('animate-pulse', className)}>\n      <div className=\"bg-gray-200 rounded-lg h-4 w-full\"></div>\n    </div>\n  )\n}\n\nexport function LoadingCard() {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-6 animate-pulse\">\n      <div className=\"space-y-4\">\n        <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n        <div className=\"space-y-2\">\n          <div className=\"h-3 bg-gray-200 rounded\"></div>\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\"></div>\n        </div>\n        <div className=\"h-8 bg-gray-200 rounded w-1/4\"></div>\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingTable({ rows = 5 }: { rows?: number }) {\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden\">\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"h-6 bg-gray-200 rounded w-1/3 animate-pulse\"></div>\n      </div>\n      <div className=\"divide-y divide-gray-200\">\n        {Array.from({ length: rows }).map((_, i) => (\n          <div key={i} className=\"p-4 animate-pulse\">\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"h-4 w-4 bg-gray-200 rounded\"></div>\n              <div className=\"flex-1 space-y-2\">\n                <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n              </div>\n              <div className=\"h-8 w-8 bg-gray-200 rounded\"></div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\nexport function LoadingStats() {\n  return (\n    <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n      {Array.from({ length: 4 }).map((_, i) => (\n        <div key={i} className=\"bg-white rounded-xl border border-gray-200 shadow-sm p-4 animate-pulse\">\n          <div className=\"text-center space-y-2\">\n            <div className=\"h-8 bg-gray-200 rounded w-16 mx-auto\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-20 mx-auto\"></div>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;AASA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAEO,SAAS,QAAQ,EACtB,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,SAAS,EACT,IAAI,EACS;IACb,IAAI,YAAY,WAAW;QACzB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,QAAQ;QACtB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;gBAEhB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,IAAI,YAAY,SAAS;QACvB,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;;8BACxE,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0CACA,WAAW,CAAC,KAAK;;;;;;gBAGpB,sBAAQ,8OAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;IAGrD;IAEA,OAAO;AACT;AAEO,SAAS,gBAAgB,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;kBAClC,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;AAEO,SAAS,aAAa,EAAE,OAAO,CAAC,EAAqB;IAC1D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAEjB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAK,GAAG,GAAG,CAAC,CAAC,GAAG,kBACpC,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;uBAPT;;;;;;;;;;;;;;;;AAcpB;AAEO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;gBAAY,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;eAHT;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Badge.tsx"], "sourcesContent": ["import { HTMLAttributes, forwardRef } from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n        secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n        destructive: 'bg-red-100 text-red-800 hover:bg-red-200',\n        success: 'bg-green-100 text-green-800 hover:bg-green-200',\n        warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',\n        outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50',\n        purple: 'bg-purple-100 text-purple-800 hover:bg-purple-200',\n        indigo: 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200',\n        pink: 'bg-pink-100 text-pink-800 hover:bg-pink-200',\n        orange: 'bg-orange-100 text-orange-800 hover:bg-orange-200',\n      },\n      size: {\n        default: 'px-2.5 py-0.5 text-xs',\n        sm: 'px-2 py-0.5 text-xs',\n        lg: 'px-3 py-1 text-sm',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nconst Badge = forwardRef<HTMLDivElement, BadgeProps>(\n  ({ className, variant, size, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(badgeVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n  }\n)\nBadge.displayName = 'Badge'\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,uGACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAOF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IACvC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;YAAS;QAAK,IAAI;QAC/C,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/ui/Input.tsx"], "sourcesContent": ["import { InputHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface InputProps extends InputHTMLAttributes<HTMLInputElement> {\n  error?: boolean\n  helperText?: string\n  label?: string\n}\n\nconst Input = forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, error, helperText, label, id, ...props }, ref) => {\n    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`\n    const helperTextId = helperText ? `${inputId}-helper` : undefined\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label\n            htmlFor={inputId}\n            className=\"block text-sm font-medium text-gray-700 mb-1\"\n          >\n            {label}\n            {props.required && <span className=\"text-red-500 ml-1\" aria-label=\"required\">*</span>}\n          </label>\n        )}\n        <input\n          id={inputId}\n          type={type}\n          className={cn(\n            'flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200 hover:border-gray-400',\n            error && 'border-red-500 focus-visible:ring-red-500 aria-invalid:border-red-500',\n            className\n          )}\n          ref={ref}\n          aria-invalid={error}\n          aria-describedby={helperTextId}\n          {...props}\n        />\n        {helperText && (\n          <p\n            id={helperTextId}\n            className={cn(\n              'mt-1 text-xs',\n              error ? 'text-red-600' : 'text-gray-500'\n            )}\n            role={error ? 'alert' : 'status'}\n          >\n            {helperText}\n          </p>\n        )}\n      </div>\n    )\n  }\n)\nInput.displayName = 'Input'\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACrB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IACxE,MAAM,eAAe,aAAa,GAAG,QAAQ,OAAO,CAAC,GAAG;IAExD,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,MAAM,QAAQ,kBAAI,8OAAC;wBAAK,WAAU;wBAAoB,cAAW;kCAAW;;;;;;;;;;;;0BAGjF,8OAAC;gBACC,IAAI;gBACJ,MAAM;gBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA,SAAS,yEACT;gBAEF,KAAK;gBACL,gBAAc;gBACd,oBAAkB;gBACjB,GAAG,KAAK;;;;;;YAEV,4BACC,8OAAC;gBACC,IAAI;gBACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBACA,QAAQ,iBAAiB;gBAE3B,MAAM,QAAQ,UAAU;0BAEvB;;;;;;;;;;;;AAKX;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/route-optimization-service.ts"], "sourcesContent": ["export interface RouteWaypoint {\n  id: string\n  name: string\n  latitude: number\n  longitude: number\n  category: 'accommodation' | 'restaurant' | 'attraction' | 'transport' | 'shopping' | 'other'\n  priority: 'high' | 'medium' | 'low'\n  estimatedDuration?: number // minutes to spend at location\n}\n\nexport interface RouteSegment {\n  from: RouteWaypoint\n  to: RouteWaypoint\n  distance: number // meters\n  duration: number // seconds\n  polyline: string\n  instructions: string[]\n}\n\nexport interface OptimizedRoute {\n  waypoints: RouteWaypoint[]\n  segments: RouteSegment[]\n  totalDistance: number // meters\n  totalDuration: number // seconds (travel time only)\n  totalTimeWithStops: number // seconds (including estimated stop times)\n  estimatedCost: {\n    fuel: number\n    tolls: number\n    parking: number\n  }\n}\n\nexport interface RouteOptimizationOptions {\n  startLocation?: { latitude: number; longitude: number }\n  endLocation?: { latitude: number; longitude: number }\n  travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'\n  avoidTolls?: boolean\n  avoidHighways?: boolean\n  optimizeOrder?: boolean\n  maxWaypoints?: number\n}\n\nclass RouteOptimizationService {\n  private directionsService: google.maps.DirectionsService | null = null\n  private distanceMatrixService: google.maps.DistanceMatrixService | null = null\n\n  constructor() {\n    if (typeof google !== 'undefined' && google.maps) {\n      this.directionsService = new google.maps.DirectionsService()\n      this.distanceMatrixService = new google.maps.DistanceMatrixService()\n    }\n  }\n\n  async optimizeRoute(\n    waypoints: RouteWaypoint[],\n    options: RouteOptimizationOptions = { travelMode: 'DRIVING' }\n  ): Promise<OptimizedRoute> {\n    if (!this.directionsService || !this.distanceMatrixService) {\n      throw new Error('Google Maps services not initialized')\n    }\n\n    if (waypoints.length < 2) {\n      throw new Error('At least 2 waypoints are required for route optimization')\n    }\n\n    let optimizedWaypoints = waypoints\n\n    // If optimization is requested and we have more than 2 waypoints\n    if (options.optimizeOrder && waypoints.length > 2) {\n      optimizedWaypoints = await this.optimizeWaypointOrder(waypoints, options)\n    }\n\n    // Calculate route segments\n    const segments = await this.calculateRouteSegments(optimizedWaypoints, options)\n\n    // Calculate totals\n    const totalDistance = segments.reduce((sum, segment) => sum + segment.distance, 0)\n    const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0)\n    const totalTimeWithStops = totalDuration + \n      optimizedWaypoints.reduce((sum, wp) => sum + (wp.estimatedDuration || 0) * 60, 0)\n\n    // Estimate costs\n    const estimatedCost = this.estimateTravelCosts(totalDistance, segments.length, options)\n\n    return {\n      waypoints: optimizedWaypoints,\n      segments,\n      totalDistance,\n      totalDuration,\n      totalTimeWithStops,\n      estimatedCost\n    }\n  }\n\n  private async optimizeWaypointOrder(\n    waypoints: RouteWaypoint[],\n    options: RouteOptimizationOptions\n  ): Promise<RouteWaypoint[]> {\n    // Use Google's distance matrix to find optimal order\n    const origins = waypoints.map(wp => new google.maps.LatLng(wp.latitude, wp.longitude))\n    const destinations = origins\n\n    return new Promise((resolve, reject) => {\n      this.distanceMatrixService!.getDistanceMatrix({\n        origins,\n        destinations,\n        travelMode: google.maps.TravelMode[options.travelMode],\n        avoidHighways: options.avoidHighways || false,\n        avoidTolls: options.avoidTolls || false\n      }, (response, status) => {\n        if (status === google.maps.DistanceMatrixStatus.OK && response) {\n          const optimizedOrder = this.solveTSP(response.rows, waypoints)\n          resolve(optimizedOrder)\n        } else {\n          console.warn('Distance matrix failed, using original order:', status)\n          resolve(waypoints)\n        }\n      })\n    })\n  }\n\n  private solveTSP(distanceMatrix: google.maps.DistanceMatrixResponseRow[], waypoints: RouteWaypoint[]): RouteWaypoint[] {\n    // Simple nearest neighbor algorithm for TSP\n    const n = waypoints.length\n    const visited = new Array(n).fill(false)\n    const result: RouteWaypoint[] = []\n    \n    // Start with the first waypoint (could be optimized to start with highest priority)\n    let current = 0\n    visited[current] = true\n    result.push(waypoints[current])\n\n    for (let i = 1; i < n; i++) {\n      let nearest = -1\n      let minDistance = Infinity\n\n      for (let j = 0; j < n; j++) {\n        if (!visited[j] && distanceMatrix[current].elements[j].distance) {\n          const distance = distanceMatrix[current].elements[j].distance.value\n          if (distance < minDistance) {\n            minDistance = distance\n            nearest = j\n          }\n        }\n      }\n\n      if (nearest !== -1) {\n        visited[nearest] = true\n        result.push(waypoints[nearest])\n        current = nearest\n      }\n    }\n\n    return result\n  }\n\n  private async calculateRouteSegments(\n    waypoints: RouteWaypoint[],\n    options: RouteOptimizationOptions\n  ): Promise<RouteSegment[]> {\n    const segments: RouteSegment[] = []\n\n    for (let i = 0; i < waypoints.length - 1; i++) {\n      const from = waypoints[i]\n      const to = waypoints[i + 1]\n\n      const segment = await this.calculateSegment(from, to, options)\n      segments.push(segment)\n    }\n\n    return segments\n  }\n\n  private calculateSegment(\n    from: RouteWaypoint,\n    to: RouteWaypoint,\n    options: RouteOptimizationOptions\n  ): Promise<RouteSegment> {\n    return new Promise((resolve, reject) => {\n      this.directionsService!.route({\n        origin: new google.maps.LatLng(from.latitude, from.longitude),\n        destination: new google.maps.LatLng(to.latitude, to.longitude),\n        travelMode: google.maps.TravelMode[options.travelMode],\n        avoidHighways: options.avoidHighways || false,\n        avoidTolls: options.avoidTolls || false\n      }, (response, status) => {\n        if (status === google.maps.DirectionsStatus.OK && response) {\n          const route = response.routes[0]\n          const leg = route.legs[0]\n\n          const segment: RouteSegment = {\n            from,\n            to,\n            distance: leg.distance?.value || 0,\n            duration: leg.duration?.value || 0,\n            polyline: route.overview_polyline,\n            instructions: leg.steps?.map(step => step.instructions) || []\n          }\n\n          resolve(segment)\n        } else {\n          reject(new Error(`Failed to calculate route segment: ${status}`))\n        }\n      })\n    })\n  }\n\n  private estimateTravelCosts(\n    totalDistance: number,\n    segmentCount: number,\n    options: RouteOptimizationOptions\n  ) {\n    const distanceKm = totalDistance / 1000\n\n    // Rough cost estimates (can be made more sophisticated)\n    const costs = {\n      fuel: 0,\n      tolls: 0,\n      parking: 0\n    }\n\n    if (options.travelMode === 'DRIVING') {\n      // Fuel cost estimation (assuming 8L/100km and $1.50/L)\n      costs.fuel = Math.round((distanceKm * 8 / 100) * 1.50 * 100) / 100\n\n      // Toll estimation (rough estimate)\n      if (!options.avoidTolls) {\n        costs.tolls = Math.round(distanceKm * 0.05 * 100) / 100\n      }\n\n      // Parking estimation (per stop)\n      costs.parking = segmentCount * 5 // $5 per stop\n    }\n\n    return costs\n  }\n\n  formatDuration(seconds: number): string {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n\n    if (hours > 0) {\n      return `${hours}h ${minutes}m`\n    }\n    return `${minutes}m`\n  }\n\n  formatDistance(meters: number): string {\n    const km = meters / 1000\n    if (km >= 1) {\n      return `${Math.round(km * 10) / 10} km`\n    }\n    return `${Math.round(meters)} m`\n  }\n}\n\nexport const routeOptimizationService = new RouteOptimizationService()\n"], "names": [], "mappings": ";;;AA0CA,MAAM;IACI,oBAA0D,KAAI;IAC9D,wBAAkE,KAAI;IAE9E,aAAc;QACZ,IAAI,OAAO,WAAW,eAAe,OAAO,IAAI,EAAE;YAChD,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,IAAI,CAAC,iBAAiB;YAC1D,IAAI,CAAC,qBAAqB,GAAG,IAAI,OAAO,IAAI,CAAC,qBAAqB;QACpE;IACF;IAEA,MAAM,cACJ,SAA0B,EAC1B,UAAoC;QAAE,YAAY;IAAU,CAAC,EACpC;QACzB,IAAI,CAAC,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC1D,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,qBAAqB;QAEzB,iEAAiE;QACjE,IAAI,QAAQ,aAAa,IAAI,UAAU,MAAM,GAAG,GAAG;YACjD,qBAAqB,MAAM,IAAI,CAAC,qBAAqB,CAAC,WAAW;QACnE;QAEA,2BAA2B;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,sBAAsB,CAAC,oBAAoB;QAEvE,mBAAmB;QACnB,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,QAAQ,EAAE;QAChF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,QAAQ,EAAE;QAChF,MAAM,qBAAqB,gBACzB,mBAAmB,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,CAAC,GAAG,iBAAiB,IAAI,CAAC,IAAI,IAAI;QAEjF,iBAAiB;QACjB,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,eAAe,SAAS,MAAM,EAAE;QAE/E,OAAO;YACL,WAAW;YACX;YACA;YACA;YACA;YACA;QACF;IACF;IAEA,MAAc,sBACZ,SAA0B,EAC1B,OAAiC,EACP;QAC1B,qDAAqD;QACrD,MAAM,UAAU,UAAU,GAAG,CAAC,CAAA,KAAM,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,EAAE,GAAG,SAAS;QACpF,MAAM,eAAe;QAErB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,qBAAqB,CAAE,iBAAiB,CAAC;gBAC5C;gBACA;gBACA,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,UAAU,CAAC;gBACtD,eAAe,QAAQ,aAAa,IAAI;gBACxC,YAAY,QAAQ,UAAU,IAAI;YACpC,GAAG,CAAC,UAAU;gBACZ,IAAI,WAAW,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,IAAI,UAAU;oBAC9D,MAAM,iBAAiB,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE;oBACpD,QAAQ;gBACV,OAAO;oBACL,QAAQ,IAAI,CAAC,iDAAiD;oBAC9D,QAAQ;gBACV;YACF;QACF;IACF;IAEQ,SAAS,cAAuD,EAAE,SAA0B,EAAmB;QACrH,4CAA4C;QAC5C,MAAM,IAAI,UAAU,MAAM;QAC1B,MAAM,UAAU,IAAI,MAAM,GAAG,IAAI,CAAC;QAClC,MAAM,SAA0B,EAAE;QAElC,oFAAoF;QACpF,IAAI,UAAU;QACd,OAAO,CAAC,QAAQ,GAAG;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;QAE9B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,UAAU,CAAC;YACf,IAAI,cAAc;YAElB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;oBAC/D,MAAM,WAAW,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK;oBACnE,IAAI,WAAW,aAAa;wBAC1B,cAAc;wBACd,UAAU;oBACZ;gBACF;YACF;YAEA,IAAI,YAAY,CAAC,GAAG;gBAClB,OAAO,CAAC,QAAQ,GAAG;gBACnB,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;gBAC9B,UAAU;YACZ;QACF;QAEA,OAAO;IACT;IAEA,MAAc,uBACZ,SAA0B,EAC1B,OAAiC,EACR;QACzB,MAAM,WAA2B,EAAE;QAEnC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,GAAG,GAAG,IAAK;YAC7C,MAAM,OAAO,SAAS,CAAC,EAAE;YACzB,MAAM,KAAK,SAAS,CAAC,IAAI,EAAE;YAE3B,MAAM,UAAU,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI;YACtD,SAAS,IAAI,CAAC;QAChB;QAEA,OAAO;IACT;IAEQ,iBACN,IAAmB,EACnB,EAAiB,EACjB,OAAiC,EACV;QACvB,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,iBAAiB,CAAE,KAAK,CAAC;gBAC5B,QAAQ,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,QAAQ,EAAE,KAAK,SAAS;gBAC5D,aAAa,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,EAAE,GAAG,SAAS;gBAC7D,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,UAAU,CAAC;gBACtD,eAAe,QAAQ,aAAa,IAAI;gBACxC,YAAY,QAAQ,UAAU,IAAI;YACpC,GAAG,CAAC,UAAU;gBACZ,IAAI,WAAW,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,UAAU;oBAC1D,MAAM,QAAQ,SAAS,MAAM,CAAC,EAAE;oBAChC,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;oBAEzB,MAAM,UAAwB;wBAC5B;wBACA;wBACA,UAAU,IAAI,QAAQ,EAAE,SAAS;wBACjC,UAAU,IAAI,QAAQ,EAAE,SAAS;wBACjC,UAAU,MAAM,iBAAiB;wBACjC,cAAc,IAAI,KAAK,EAAE,IAAI,CAAA,OAAQ,KAAK,YAAY,KAAK,EAAE;oBAC/D;oBAEA,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,mCAAmC,EAAE,QAAQ;gBACjE;YACF;QACF;IACF;IAEQ,oBACN,aAAqB,EACrB,YAAoB,EACpB,OAAiC,EACjC;QACA,MAAM,aAAa,gBAAgB;QAEnC,wDAAwD;QACxD,MAAM,QAAQ;YACZ,MAAM;YACN,OAAO;YACP,SAAS;QACX;QAEA,IAAI,QAAQ,UAAU,KAAK,WAAW;YACpC,uDAAuD;YACvD,MAAM,IAAI,GAAG,KAAK,KAAK,CAAC,AAAC,aAAa,IAAI,MAAO,OAAO,OAAO;YAE/D,mCAAmC;YACnC,IAAI,CAAC,QAAQ,UAAU,EAAE;gBACvB,MAAM,KAAK,GAAG,KAAK,KAAK,CAAC,aAAa,OAAO,OAAO;YACtD;YAEA,gCAAgC;YAChC,MAAM,OAAO,GAAG,eAAe,EAAE,cAAc;;QACjD;QAEA,OAAO;IACT;IAEA,eAAe,OAAe,EAAU;QACtC,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAE9C,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC;QACA,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,eAAe,MAAc,EAAU;QACrC,MAAM,KAAK,SAAS;QACpB,IAAI,MAAM,GAAG;YACX,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG,GAAG,CAAC;QACzC;QACA,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;IAClC;AACF;AAEO,MAAM,2BAA2B,IAAI", "debugId": null}}, {"offset": {"line": 937, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/RouteOptimizer.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport {\n  RouteIcon,\n  ClockIcon,\n  MapPinIcon,\n  DollarSignIcon,\n  NavigationIcon,\n  SettingsIcon,\n  ZapIcon\n} from 'lucide-react'\nimport { routeOptimizationService, type RouteWaypoint, type OptimizedRoute, type RouteOptimizationOptions } from '@/lib/services/route-optimization-service'\nimport toast from 'react-hot-toast'\n\ninterface RouteOptimizerProps {\n  waypoints: RouteWaypoint[]\n  onRouteOptimized: (route: OptimizedRoute) => void\n  className?: string\n}\n\nexport default function RouteOptimizer({ waypoints, onRouteOptimized, className = '' }: RouteOptimizerProps) {\n  const [isOptimizing, setIsOptimizing] = useState(false)\n  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null)\n  const [showSettings, setShowSettings] = useState(false)\n  const [options, setOptions] = useState<RouteOptimizationOptions>({\n    travelMode: 'DRIVING',\n    optimizeOrder: true,\n    avoidTolls: false,\n    avoidHighways: false\n  })\n\n  const handleOptimizeRoute = async () => {\n    if (waypoints.length < 2) {\n      toast.error('At least 2 destinations are required for route optimization')\n      return\n    }\n\n    setIsOptimizing(true)\n    try {\n      const route = await routeOptimizationService.optimizeRoute(waypoints, options)\n      setOptimizedRoute(route)\n      onRouteOptimized(route)\n      toast.success('Route optimized successfully!')\n    } catch (error) {\n      console.error('Route optimization failed:', error)\n      toast.error(`Failed to optimize route: ${error.message}`)\n    } finally {\n      setIsOptimizing(false)\n    }\n  }\n\n  const getTravelModeIcon = (mode: string) => {\n    switch (mode) {\n      case 'DRIVING': return '🚗'\n      case 'WALKING': return '🚶'\n      case 'TRANSIT': return '🚌'\n      case 'BICYCLING': return '🚴'\n      default: return '🚗'\n    }\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'accommodation': return '🏨'\n      case 'restaurant': return '🍽️'\n      case 'attraction': return '🎯'\n      case 'transport': return '🚌'\n      case 'shopping': return '🛍️'\n      default: return '📍'\n    }\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <RouteIcon className=\"h-5 w-5 text-blue-600\" />\n            <h3 className=\"font-semibold text-gray-900\">Route Optimization</h3>\n            <Badge variant=\"secondary\">{waypoints.length} stops</Badge>\n          </div>\n          \n          <div className=\"flex items-center space-x-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => setShowSettings(!showSettings)}\n            >\n              <SettingsIcon className=\"h-4 w-4 mr-1\" />\n              Settings\n            </Button>\n            \n            <Button\n              onClick={handleOptimizeRoute}\n              disabled={isOptimizing || waypoints.length < 2}\n              size=\"sm\"\n            >\n              {isOptimizing ? (\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\" />\n              ) : (\n                <ZapIcon className=\"h-4 w-4 mr-2\" />\n              )}\n              {isOptimizing ? 'Optimizing...' : 'Optimize Route'}\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Settings Panel */}\n      {showSettings && (\n        <div className=\"p-4 bg-gray-50 border-b border-gray-200\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Travel Mode\n              </label>\n              <select\n                value={options.travelMode}\n                onChange={(e) => setOptions({ ...options, travelMode: e.target.value as any })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"DRIVING\">🚗 Driving</option>\n                <option value=\"WALKING\">🚶 Walking</option>\n                <option value=\"TRANSIT\">🚌 Public Transit</option>\n                <option value=\"BICYCLING\">🚴 Bicycling</option>\n              </select>\n            </div>\n\n            <div className=\"space-y-3\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={options.optimizeOrder}\n                  onChange={(e) => setOptions({ ...options, optimizeOrder: e.target.checked })}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Optimize stop order</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={options.avoidTolls}\n                  onChange={(e) => setOptions({ ...options, avoidTolls: e.target.checked })}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Avoid tolls</span>\n              </label>\n\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={options.avoidHighways}\n                  onChange={(e) => setOptions({ ...options, avoidHighways: e.target.checked })}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Avoid highways</span>\n              </label>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Route Summary */}\n      {optimizedRoute && (\n        <div className=\"p-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\">\n            <div className=\"flex items-center space-x-2\">\n              <ClockIcon className=\"h-4 w-4 text-gray-500\" />\n              <div>\n                <p className=\"text-sm text-gray-600\">Travel Time</p>\n                <p className=\"font-semibold\">{routeOptimizationService.formatDuration(optimizedRoute.totalDuration)}</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <NavigationIcon className=\"h-4 w-4 text-gray-500\" />\n              <div>\n                <p className=\"text-sm text-gray-600\">Distance</p>\n                <p className=\"font-semibold\">{routeOptimizationService.formatDistance(optimizedRoute.totalDistance)}</p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <DollarSignIcon className=\"h-4 w-4 text-gray-500\" />\n              <div>\n                <p className=\"text-sm text-gray-600\">Est. Cost</p>\n                <p className=\"font-semibold\">\n                  ${(optimizedRoute.estimatedCost.fuel + optimizedRoute.estimatedCost.tolls + optimizedRoute.estimatedCost.parking).toFixed(2)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Route Steps */}\n          <div className=\"space-y-2\">\n            <h4 className=\"font-medium text-gray-900 mb-3\">Optimized Route</h4>\n            {optimizedRoute.waypoints.map((waypoint, index) => (\n              <div key={waypoint.id} className=\"flex items-center space-x-3 p-2 bg-gray-50 rounded-lg\">\n                <div className=\"flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-semibold\">\n                  {index + 1}\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-lg\">{getCategoryIcon(waypoint.category)}</span>\n                    <span className=\"font-medium text-gray-900\">{waypoint.name}</span>\n                    <Badge variant={waypoint.priority === 'high' ? 'destructive' : waypoint.priority === 'medium' ? 'default' : 'secondary'}>\n                      {waypoint.priority}\n                    </Badge>\n                  </div>\n                  {waypoint.estimatedDuration && (\n                    <p className=\"text-sm text-gray-600\">Est. {waypoint.estimatedDuration} min visit</p>\n                  )}\n                </div>\n                {index < optimizedRoute.segments.length && (\n                  <div className=\"text-sm text-gray-500\">\n                    {routeOptimizationService.formatDuration(optimizedRoute.segments[index].duration)} →\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {/* Cost Breakdown */}\n          {options.travelMode === 'DRIVING' && (\n            <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n              <h5 className=\"font-medium text-blue-900 mb-2\">Cost Breakdown</h5>\n              <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <p className=\"text-blue-700\">Fuel</p>\n                  <p className=\"font-semibold text-blue-900\">${optimizedRoute.estimatedCost.fuel.toFixed(2)}</p>\n                </div>\n                <div>\n                  <p className=\"text-blue-700\">Tolls</p>\n                  <p className=\"font-semibold text-blue-900\">${optimizedRoute.estimatedCost.tolls.toFixed(2)}</p>\n                </div>\n                <div>\n                  <p className=\"text-blue-700\">Parking</p>\n                  <p className=\"font-semibold text-blue-900\">${optimizedRoute.estimatedCost.parking.toFixed(2)}</p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Empty State */}\n      {waypoints.length < 2 && (\n        <div className=\"p-8 text-center text-gray-500\">\n          <MapPinIcon className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n          <p>Add at least 2 destinations to optimize your route</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAfA;;;;;;;;AAuBe,SAAS,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAuB;IACzG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B;QAC/D,YAAY;QACZ,eAAe;QACf,YAAY;QACZ,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,QAAQ,MAAM,0JAAA,CAAA,2BAAwB,CAAC,aAAa,CAAC,WAAW;YACtE,kBAAkB;YAClB,iBAAiB;YACjB,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,0BAA0B,EAAE,MAAM,OAAO,EAAE;QAC1D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BACjF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wMAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;;wCAAa,UAAU,MAAM;wCAAC;;;;;;;;;;;;;sCAG/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;;sDAEhC,8OAAC,8MAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAI3C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,gBAAgB,UAAU,MAAM,GAAG;oCAC7C,MAAK;;wCAEJ,6BACC,8OAAC;4CAAI,WAAU;;;;;iEAEf,8OAAC,oMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAEpB,eAAe,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;YAOzC,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO,QAAQ,UAAU;oCACzB,UAAU,CAAC,IAAM,WAAW;4CAAE,GAAG,OAAO;4CAAE,YAAY,EAAE,MAAM,CAAC,KAAK;wCAAQ;oCAC5E,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,8OAAC;4CAAO,OAAM;sDAAY;;;;;;;;;;;;;;;;;;sCAI9B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,aAAa;4CAC9B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,eAAe,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC1E,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAG/C,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,UAAU;4CAC3B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,YAAY,EAAE,MAAM,CAAC,OAAO;gDAAC;4CACvE,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;8CAG/C,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS,QAAQ,aAAa;4CAC9B,UAAU,CAAC,IAAM,WAAW;oDAAE,GAAG,OAAO;oDAAE,eAAe,EAAE,MAAM,CAAC,OAAO;gDAAC;4CAC1E,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtD,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiB,0JAAA,CAAA,2BAAwB,CAAC,cAAc,CAAC,eAAe,aAAa;;;;;;;;;;;;;;;;;;0CAItG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;0DAAiB,0JAAA,CAAA,2BAAwB,CAAC,cAAc,CAAC,eAAe,aAAa;;;;;;;;;;;;;;;;;;0CAItG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAC1B,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DACrC,8OAAC;gDAAE,WAAU;;oDAAgB;oDACzB,CAAC,eAAe,aAAa,CAAC,IAAI,GAAG,eAAe,aAAa,CAAC,KAAK,GAAG,eAAe,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlI,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;4BAC9C,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACvC,8OAAC;oCAAsB,WAAU;;sDAC/B,8OAAC;4CAAI,WAAU;sDACZ,QAAQ;;;;;;sDAEX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAW,gBAAgB,SAAS,QAAQ;;;;;;sEAC5D,8OAAC;4DAAK,WAAU;sEAA6B,SAAS,IAAI;;;;;;sEAC1D,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAS,SAAS,QAAQ,KAAK,SAAS,gBAAgB,SAAS,QAAQ,KAAK,WAAW,YAAY;sEACzG,SAAS,QAAQ;;;;;;;;;;;;gDAGrB,SAAS,iBAAiB,kBACzB,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAM,SAAS,iBAAiB;wDAAC;;;;;;;;;;;;;wCAGzE,QAAQ,eAAe,QAAQ,CAAC,MAAM,kBACrC,8OAAC;4CAAI,WAAU;;gDACZ,0JAAA,CAAA,2BAAwB,CAAC,cAAc,CAAC,eAAe,QAAQ,CAAC,MAAM,CAAC,QAAQ;gDAAE;;;;;;;;mCAlB9E,SAAS,EAAE;;;;;;;;;;;oBA0BxB,QAAQ,UAAU,KAAK,2BACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;;oDAA8B;oDAAE,eAAe,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAEzF,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;;oDAA8B;oDAAE,eAAe,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;kDAE1F,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAC7B,8OAAC;gDAAE,WAAU;;oDAA8B;oDAAE,eAAe,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASrG,UAAU,MAAM,GAAG,mBAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;kCACtB,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/services/travel-time-service.ts"], "sourcesContent": ["export interface TravelTimeRequest {\n  origin: {\n    latitude: number\n    longitude: number\n    name?: string\n  }\n  destination: {\n    latitude: number\n    longitude: number\n    name?: string\n  }\n  travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'\n  departureTime?: Date\n  arrivalTime?: Date\n}\n\nexport interface TravelTimeResult {\n  distance: {\n    text: string\n    value: number // meters\n  }\n  duration: {\n    text: string\n    value: number // seconds\n  }\n  durationInTraffic?: {\n    text: string\n    value: number // seconds\n  }\n  status: string\n  travelMode: string\n}\n\nexport interface TravelMatrix {\n  origins: Array<{ latitude: number; longitude: number; name?: string }>\n  destinations: Array<{ latitude: number; longitude: number; name?: string }>\n  results: TravelTimeResult[][]\n  travelMode: string\n}\n\nclass TravelTimeService {\n  private distanceMatrixService: google.maps.DistanceMatrixService | null = null\n  private directionsService: google.maps.DirectionsService | null = null\n\n  constructor() {\n    if (typeof google !== 'undefined' && google.maps) {\n      this.distanceMatrixService = new google.maps.DistanceMatrixService()\n      this.directionsService = new google.maps.DirectionsService()\n    }\n  }\n\n  async calculateTravelTime(request: TravelTimeRequest): Promise<TravelTimeResult> {\n    if (!this.distanceMatrixService) {\n      throw new Error('Google Maps Distance Matrix service not initialized')\n    }\n\n    return new Promise((resolve, reject) => {\n      const origins = [new google.maps.LatLng(request.origin.latitude, request.origin.longitude)]\n      const destinations = [new google.maps.LatLng(request.destination.latitude, request.destination.longitude)]\n\n      const matrixRequest: google.maps.DistanceMatrixRequest = {\n        origins,\n        destinations,\n        travelMode: google.maps.TravelMode[request.travelMode],\n        unitSystem: google.maps.UnitSystem.METRIC,\n        avoidHighways: false,\n        avoidTolls: false\n      }\n\n      // Add departure time for driving mode to get traffic data\n      if (request.travelMode === 'DRIVING' && request.departureTime) {\n        matrixRequest.drivingOptions = {\n          departureTime: request.departureTime,\n          trafficModel: google.maps.TrafficModel.BEST_GUESS\n        }\n      }\n\n      // Add transit options for public transport\n      if (request.travelMode === 'TRANSIT') {\n        matrixRequest.transitOptions = {\n          departureTime: request.departureTime,\n          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],\n          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS\n        }\n      }\n\n      this.distanceMatrixService.getDistanceMatrix(matrixRequest, (response, status) => {\n        if (status === google.maps.DistanceMatrixStatus.OK && response) {\n          const element = response.rows[0].elements[0]\n          \n          if (element.status === google.maps.DistanceMatrixElementStatus.OK) {\n            const result: TravelTimeResult = {\n              distance: element.distance!,\n              duration: element.duration!,\n              status: element.status,\n              travelMode: request.travelMode\n            }\n\n            // Add traffic duration if available\n            if (element.duration_in_traffic) {\n              result.durationInTraffic = element.duration_in_traffic\n            }\n\n            resolve(result)\n          } else {\n            reject(new Error(`Travel time calculation failed: ${element.status}`))\n          }\n        } else {\n          reject(new Error(`Distance Matrix API failed: ${status}`))\n        }\n      })\n    })\n  }\n\n  async calculateTravelMatrix(\n    origins: Array<{ latitude: number; longitude: number; name?: string }>,\n    destinations: Array<{ latitude: number; longitude: number; name?: string }>,\n    travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING' = 'DRIVING',\n    departureTime?: Date\n  ): Promise<TravelMatrix> {\n    if (!this.distanceMatrixService) {\n      throw new Error('Google Maps Distance Matrix service not initialized')\n    }\n\n    return new Promise((resolve, reject) => {\n      const googleOrigins = origins.map(o => new google.maps.LatLng(o.latitude, o.longitude))\n      const googleDestinations = destinations.map(d => new google.maps.LatLng(d.latitude, d.longitude))\n\n      const matrixRequest: google.maps.DistanceMatrixRequest = {\n        origins: googleOrigins,\n        destinations: googleDestinations,\n        travelMode: google.maps.TravelMode[travelMode],\n        unitSystem: google.maps.UnitSystem.METRIC,\n        avoidHighways: false,\n        avoidTolls: false\n      }\n\n      if (travelMode === 'DRIVING' && departureTime) {\n        matrixRequest.drivingOptions = {\n          departureTime,\n          trafficModel: google.maps.TrafficModel.BEST_GUESS\n        }\n      }\n\n      if (travelMode === 'TRANSIT') {\n        matrixRequest.transitOptions = {\n          departureTime: departureTime || new Date(),\n          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],\n          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS\n        }\n      }\n\n      this.distanceMatrixService.getDistanceMatrix(matrixRequest, (response, status) => {\n        if (status === google.maps.DistanceMatrixStatus.OK && response) {\n          const results: TravelTimeResult[][] = []\n\n          response.rows.forEach((row, originIndex) => {\n            results[originIndex] = []\n            row.elements.forEach((element, destIndex) => {\n              if (element.status === google.maps.DistanceMatrixElementStatus.OK) {\n                const result: TravelTimeResult = {\n                  distance: element.distance!,\n                  duration: element.duration!,\n                  status: element.status,\n                  travelMode\n                }\n\n                if (element.duration_in_traffic) {\n                  result.durationInTraffic = element.duration_in_traffic\n                }\n\n                results[originIndex][destIndex] = result\n              } else {\n                results[originIndex][destIndex] = {\n                  distance: { text: 'N/A', value: 0 },\n                  duration: { text: 'N/A', value: 0 },\n                  status: element.status,\n                  travelMode\n                }\n              }\n            })\n          })\n\n          resolve({\n            origins,\n            destinations,\n            results,\n            travelMode\n          })\n        } else {\n          reject(new Error(`Distance Matrix API failed: ${status}`))\n        }\n      })\n    })\n  }\n\n  async getDetailedDirections(\n    origin: { latitude: number; longitude: number },\n    destination: { latitude: number; longitude: number },\n    travelMode: 'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING' = 'DRIVING',\n    waypoints?: Array<{ latitude: number; longitude: number }>,\n    departureTime?: Date\n  ): Promise<google.maps.DirectionsResult> {\n    if (!this.directionsService) {\n      throw new Error('Google Maps Directions service not initialized')\n    }\n\n    return new Promise((resolve, reject) => {\n      const request: google.maps.DirectionsRequest = {\n        origin: new google.maps.LatLng(origin.latitude, origin.longitude),\n        destination: new google.maps.LatLng(destination.latitude, destination.longitude),\n        travelMode: google.maps.TravelMode[travelMode],\n        unitSystem: google.maps.UnitSystem.METRIC\n      }\n\n      if (waypoints && waypoints.length > 0) {\n        request.waypoints = waypoints.map(wp => ({\n          location: new google.maps.LatLng(wp.latitude, wp.longitude),\n          stopover: true\n        }))\n      }\n\n      if (travelMode === 'DRIVING' && departureTime) {\n        request.drivingOptions = {\n          departureTime,\n          trafficModel: google.maps.TrafficModel.BEST_GUESS\n        }\n      }\n\n      if (travelMode === 'TRANSIT') {\n        request.transitOptions = {\n          departureTime: departureTime || new Date(),\n          modes: [google.maps.TransitMode.BUS, google.maps.TransitMode.RAIL, google.maps.TransitMode.SUBWAY],\n          routingPreference: google.maps.TransitRoutePreference.BEST_GUESS\n        }\n      }\n\n      this.directionsService.route(request, (result, status) => {\n        if (status === google.maps.DirectionsStatus.OK && result) {\n          resolve(result)\n        } else {\n          reject(new Error(`Directions request failed: ${status}`))\n        }\n      })\n    })\n  }\n\n  formatDuration(seconds: number, includeTraffic: boolean = false): string {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n\n    if (hours > 0) {\n      return `${hours}h ${minutes}m`\n    }\n    return `${minutes}m`\n  }\n\n  formatDistance(meters: number): string {\n    const km = meters / 1000\n    if (km >= 1) {\n      return `${Math.round(km * 10) / 10} km`\n    }\n    return `${Math.round(meters)} m`\n  }\n\n  getTravelModeIcon(mode: string): string {\n    switch (mode) {\n      case 'DRIVING': return '🚗'\n      case 'WALKING': return '🚶'\n      case 'TRANSIT': return '🚌'\n      case 'BICYCLING': return '🚴'\n      default: return '🚗'\n    }\n  }\n\n  estimateArrivalTime(departureTime: Date, durationSeconds: number): Date {\n    return new Date(departureTime.getTime() + durationSeconds * 1000)\n  }\n}\n\nexport const travelTimeService = new TravelTimeService()\n"], "names": [], "mappings": ";;;AAwCA,MAAM;IACI,wBAAkE,KAAI;IACtE,oBAA0D,KAAI;IAEtE,aAAc;QACZ,IAAI,OAAO,WAAW,eAAe,OAAO,IAAI,EAAE;YAChD,IAAI,CAAC,qBAAqB,GAAG,IAAI,OAAO,IAAI,CAAC,qBAAqB;YAClE,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,IAAI,CAAC,iBAAiB;QAC5D;IACF;IAEA,MAAM,oBAAoB,OAA0B,EAA6B;QAC/E,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAAU;gBAAC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS;aAAE;YAC3F,MAAM,eAAe;gBAAC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,WAAW,CAAC,QAAQ,EAAE,QAAQ,WAAW,CAAC,SAAS;aAAE;YAE1G,MAAM,gBAAmD;gBACvD;gBACA;gBACA,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,UAAU,CAAC;gBACtD,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;gBACzC,eAAe;gBACf,YAAY;YACd;YAEA,0DAA0D;YAC1D,IAAI,QAAQ,UAAU,KAAK,aAAa,QAAQ,aAAa,EAAE;gBAC7D,cAAc,cAAc,GAAG;oBAC7B,eAAe,QAAQ,aAAa;oBACpC,cAAc,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;gBACnD;YACF;YAEA,2CAA2C;YAC3C,IAAI,QAAQ,UAAU,KAAK,WAAW;gBACpC,cAAc,cAAc,GAAG;oBAC7B,eAAe,QAAQ,aAAa;oBACpC,OAAO;wBAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;qBAAC;oBAClG,mBAAmB,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU;gBAClE;YACF;YAEA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,UAAU;gBACrE,IAAI,WAAW,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,IAAI,UAAU;oBAC9D,MAAM,UAAU,SAAS,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;oBAE5C,IAAI,QAAQ,MAAM,KAAK,OAAO,IAAI,CAAC,2BAA2B,CAAC,EAAE,EAAE;wBACjE,MAAM,SAA2B;4BAC/B,UAAU,QAAQ,QAAQ;4BAC1B,UAAU,QAAQ,QAAQ;4BAC1B,QAAQ,QAAQ,MAAM;4BACtB,YAAY,QAAQ,UAAU;wBAChC;wBAEA,oCAAoC;wBACpC,IAAI,QAAQ,mBAAmB,EAAE;4BAC/B,OAAO,iBAAiB,GAAG,QAAQ,mBAAmB;wBACxD;wBAEA,QAAQ;oBACV,OAAO;wBACL,OAAO,IAAI,MAAM,CAAC,gCAAgC,EAAE,QAAQ,MAAM,EAAE;oBACtE;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;gBAC1D;YACF;QACF;IACF;IAEA,MAAM,sBACJ,OAAsE,EACtE,YAA2E,EAC3E,aAA8D,SAAS,EACvE,aAAoB,EACG;QACvB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAA,IAAK,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS;YACrF,MAAM,qBAAqB,aAAa,GAAG,CAAC,CAAA,IAAK,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,EAAE,SAAS;YAE/F,MAAM,gBAAmD;gBACvD,SAAS;gBACT,cAAc;gBACd,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW;gBAC9C,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;gBACzC,eAAe;gBACf,YAAY;YACd;YAEA,IAAI,eAAe,aAAa,eAAe;gBAC7C,cAAc,cAAc,GAAG;oBAC7B;oBACA,cAAc,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;gBACnD;YACF;YAEA,IAAI,eAAe,WAAW;gBAC5B,cAAc,cAAc,GAAG;oBAC7B,eAAe,iBAAiB,IAAI;oBACpC,OAAO;wBAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;qBAAC;oBAClG,mBAAmB,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU;gBAClE;YACF;YAEA,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,eAAe,CAAC,UAAU;gBACrE,IAAI,WAAW,OAAO,IAAI,CAAC,oBAAoB,CAAC,EAAE,IAAI,UAAU;oBAC9D,MAAM,UAAgC,EAAE;oBAExC,SAAS,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK;wBAC1B,OAAO,CAAC,YAAY,GAAG,EAAE;wBACzB,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS;4BAC7B,IAAI,QAAQ,MAAM,KAAK,OAAO,IAAI,CAAC,2BAA2B,CAAC,EAAE,EAAE;gCACjE,MAAM,SAA2B;oCAC/B,UAAU,QAAQ,QAAQ;oCAC1B,UAAU,QAAQ,QAAQ;oCAC1B,QAAQ,QAAQ,MAAM;oCACtB;gCACF;gCAEA,IAAI,QAAQ,mBAAmB,EAAE;oCAC/B,OAAO,iBAAiB,GAAG,QAAQ,mBAAmB;gCACxD;gCAEA,OAAO,CAAC,YAAY,CAAC,UAAU,GAAG;4BACpC,OAAO;gCACL,OAAO,CAAC,YAAY,CAAC,UAAU,GAAG;oCAChC,UAAU;wCAAE,MAAM;wCAAO,OAAO;oCAAE;oCAClC,UAAU;wCAAE,MAAM;wCAAO,OAAO;oCAAE;oCAClC,QAAQ,QAAQ,MAAM;oCACtB;gCACF;4BACF;wBACF;oBACF;oBAEA,QAAQ;wBACN;wBACA;wBACA;wBACA;oBACF;gBACF,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,4BAA4B,EAAE,QAAQ;gBAC1D;YACF;QACF;IACF;IAEA,MAAM,sBACJ,MAA+C,EAC/C,WAAoD,EACpD,aAA8D,SAAS,EACvE,SAA0D,EAC1D,aAAoB,EACmB;QACvC,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAAyC;gBAC7C,QAAQ,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,QAAQ,EAAE,OAAO,SAAS;gBAChE,aAAa,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,QAAQ,EAAE,YAAY,SAAS;gBAC/E,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW;gBAC9C,YAAY,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM;YAC3C;YAEA,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,QAAQ,SAAS,GAAG,UAAU,GAAG,CAAC,CAAA,KAAM,CAAC;wBACvC,UAAU,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,QAAQ,EAAE,GAAG,SAAS;wBAC1D,UAAU;oBACZ,CAAC;YACH;YAEA,IAAI,eAAe,aAAa,eAAe;gBAC7C,QAAQ,cAAc,GAAG;oBACvB;oBACA,cAAc,OAAO,IAAI,CAAC,YAAY,CAAC,UAAU;gBACnD;YACF;YAEA,IAAI,eAAe,WAAW;gBAC5B,QAAQ,cAAc,GAAG;oBACvB,eAAe,iBAAiB,IAAI;oBACpC,OAAO;wBAAC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;wBAAE,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;qBAAC;oBAClG,mBAAmB,OAAO,IAAI,CAAC,sBAAsB,CAAC,UAAU;gBAClE;YACF;YAEA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ;gBAC7C,IAAI,WAAW,OAAO,IAAI,CAAC,gBAAgB,CAAC,EAAE,IAAI,QAAQ;oBACxD,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM,CAAC,2BAA2B,EAAE,QAAQ;gBACzD;YACF;QACF;IACF;IAEA,eAAe,OAAe,EAAE,iBAA0B,KAAK,EAAU;QACvE,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAE9C,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAChC;QACA,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,eAAe,MAAc,EAAU;QACrC,MAAM,KAAK,SAAS;QACpB,IAAI,MAAM,GAAG;YACX,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG,GAAG,CAAC;QACzC;QACA,OAAO,GAAG,KAAK,KAAK,CAAC,QAAQ,EAAE,CAAC;IAClC;IAEA,kBAAkB,IAAY,EAAU;QACtC,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,oBAAoB,aAAmB,EAAE,eAAuB,EAAQ;QACtE,OAAO,IAAI,KAAK,cAAc,OAAO,KAAK,kBAAkB;IAC9D;AACF;AAEO,MAAM,oBAAoB,IAAI", "debugId": null}}, {"offset": {"line": 1920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/TravelTimeMatrix.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Badge } from '@/components/ui/Badge'\nimport { \n  ClockIcon, \n  NavigationIcon, \n  RefreshCwIcon,\n  MapPinIcon,\n  CalendarIcon,\n  AlertCircleIcon\n} from 'lucide-react'\nimport { travelTimeService, type TravelMatrix, type TravelTimeRequest } from '@/lib/services/travel-time-service'\nimport toast from 'react-hot-toast'\n\ninterface TravelTimeMatrixProps {\n  destinations: Array<{\n    id: string\n    name: string\n    latitude: number\n    longitude: number\n    category: string\n  }>\n  className?: string\n}\n\nexport default function TravelTimeMatrix({ destinations, className = '' }: TravelTimeMatrixProps) {\n  const [travelMatrix, setTravelMatrix] = useState<TravelMatrix | null>(null)\n  const [isLoading, setIsLoading] = useState(false)\n  const [travelMode, setTravelMode] = useState<'DRIVING' | 'WALKING' | 'TRANSIT' | 'BICYCLING'>('DRIVING')\n  const [departureTime, setDepartureTime] = useState<Date>(new Date())\n  const [showTrafficData, setShowTrafficData] = useState(true)\n\n  useEffect(() => {\n    if (destinations.length >= 2) {\n      calculateTravelTimes()\n    }\n  }, [destinations, travelMode])\n\n  const calculateTravelTimes = async () => {\n    if (destinations.length < 2) {\n      toast.error('At least 2 destinations are required')\n      return\n    }\n\n    setIsLoading(true)\n    try {\n      const matrix = await travelTimeService.calculateTravelMatrix(\n        destinations,\n        destinations,\n        travelMode,\n        departureTime\n      )\n      setTravelMatrix(matrix)\n    } catch (error) {\n      console.error('Failed to calculate travel times:', error)\n      toast.error(`Failed to calculate travel times: ${error.message}`)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const getTravelModeColor = (mode: string) => {\n    switch (mode) {\n      case 'DRIVING': return 'bg-blue-100 text-blue-800'\n      case 'WALKING': return 'bg-green-100 text-green-800'\n      case 'TRANSIT': return 'bg-purple-100 text-purple-800'\n      case 'BICYCLING': return 'bg-orange-100 text-orange-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getCategoryIcon = (category: string) => {\n    switch (category) {\n      case 'accommodation': return '🏨'\n      case 'restaurant': return '🍽️'\n      case 'attraction': return '🎯'\n      case 'transport': return '🚌'\n      case 'shopping': return '🛍️'\n      default: return '📍'\n    }\n  }\n\n  const formatDateTime = (date: Date) => {\n    return date.toLocaleString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    })\n  }\n\n  if (destinations.length < 2) {\n    return (\n      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center ${className}`}>\n        <MapPinIcon className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n        <p className=\"text-gray-500\">Add at least 2 destinations to see travel times</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            <ClockIcon className=\"h-5 w-5 text-blue-600\" />\n            <h3 className=\"font-semibold text-gray-900\">Travel Times</h3>\n            <Badge variant=\"secondary\">{destinations.length} destinations</Badge>\n          </div>\n          \n          <Button\n            onClick={calculateTravelTimes}\n            disabled={isLoading}\n            size=\"sm\"\n            variant=\"outline\"\n          >\n            {isLoading ? (\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2\" />\n            ) : (\n              <RefreshCwIcon className=\"h-4 w-4 mr-2\" />\n            )}\n            {isLoading ? 'Calculating...' : 'Refresh'}\n          </Button>\n        </div>\n\n        {/* Controls */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Travel Mode\n            </label>\n            <select\n              value={travelMode}\n              onChange={(e) => setTravelMode(e.target.value as any)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            >\n              <option value=\"DRIVING\">🚗 Driving</option>\n              <option value=\"WALKING\">🚶 Walking</option>\n              <option value=\"TRANSIT\">🚌 Public Transit</option>\n              <option value=\"BICYCLING\">🚴 Bicycling</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Departure Time\n            </label>\n            <input\n              type=\"datetime-local\"\n              value={departureTime.toISOString().slice(0, 16)}\n              onChange={(e) => setDepartureTime(new Date(e.target.value))}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            />\n          </div>\n\n          {travelMode === 'DRIVING' && (\n            <div className=\"flex items-end\">\n              <label className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  checked={showTrafficData}\n                  onChange={(e) => setShowTrafficData(e.target.checked)}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"ml-2 text-sm text-gray-700\">Include traffic data</span>\n              </label>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Travel Time Matrix */}\n      {travelMatrix && (\n        <div className=\"p-4\">\n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full\">\n              <thead>\n                <tr>\n                  <th className=\"text-left p-2 text-sm font-medium text-gray-700\">From / To</th>\n                  {travelMatrix.destinations.map((dest, index) => (\n                    <th key={index} className=\"text-center p-2 text-sm font-medium text-gray-700 min-w-[120px]\">\n                      <div className=\"flex flex-col items-center space-y-1\">\n                        <span className=\"text-lg\">{getCategoryIcon(destinations[index].category)}</span>\n                        <span className=\"text-xs truncate max-w-[100px]\">{dest.name}</span>\n                      </div>\n                    </th>\n                  ))}\n                </tr>\n              </thead>\n              <tbody>\n                {travelMatrix.origins.map((origin, originIndex) => (\n                  <tr key={originIndex} className=\"border-t border-gray-200\">\n                    <td className=\"p-2 font-medium text-gray-900\">\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-lg\">{getCategoryIcon(destinations[originIndex].category)}</span>\n                        <span className=\"text-sm truncate max-w-[120px]\">{origin.name}</span>\n                      </div>\n                    </td>\n                    {travelMatrix.results[originIndex].map((result, destIndex) => (\n                      <td key={destIndex} className=\"p-2 text-center\">\n                        {originIndex === destIndex ? (\n                          <div className=\"text-gray-400 text-sm\">—</div>\n                        ) : result.status === 'OK' ? (\n                          <div className=\"space-y-1\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {result.duration.text}\n                            </div>\n                            <div className=\"text-xs text-gray-600\">\n                              {result.distance.text}\n                            </div>\n                            {result.durationInTraffic && showTrafficData && (\n                              <div className=\"text-xs text-orange-600\">\n                                {result.durationInTraffic.text} (traffic)\n                              </div>\n                            )}\n                          </div>\n                        ) : (\n                          <div className=\"text-red-500 text-xs\">\n                            <AlertCircleIcon className=\"h-4 w-4 mx-auto mb-1\" />\n                            N/A\n                          </div>\n                        )}\n                      </td>\n                    ))}\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n\n          {/* Summary */}\n          <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n            <div className=\"flex items-center justify-between text-sm\">\n              <div className=\"flex items-center space-x-4\">\n                <Badge className={getTravelModeColor(travelMode)}>\n                  {travelTimeService.getTravelModeIcon(travelMode)} {travelMode}\n                </Badge>\n                <span className=\"text-gray-600\">\n                  <CalendarIcon className=\"h-4 w-4 inline mr-1\" />\n                  {formatDateTime(departureTime)}\n                </span>\n              </div>\n              <div className=\"text-gray-600\">\n                Matrix: {destinations.length}×{destinations.length} destinations\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"p-8 text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Calculating travel times...</p>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAdA;;;;;;;;AA2Be,SAAS,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,EAAyB;IAC9F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IAC9F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,MAAM,IAAI,GAAG;YAC5B;QACF;IACF,GAAG;QAAC;QAAc;KAAW;IAE7B,MAAM,uBAAuB;QAC3B,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,mJAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAC1D,cACA,cACA,YACA;YAEF,gBAAgB;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,kCAAkC,EAAE,MAAM,OAAO,EAAE;QAClE,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAiB,OAAO;YAC7B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,KAAK,cAAc,CAAC,SAAS;YAClC,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,qBACE,8OAAC;YAAI,WAAW,CAAC,qEAAqE,EAAE,WAAW;;8BACjG,8OAAC,8MAAA,CAAA,aAAU;oBAAC,WAAU;;;;;;8BACtB,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BACjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAG,WAAU;kDAA8B;;;;;;kDAC5C,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;4CAAa,aAAa,MAAM;4CAAC;;;;;;;;;;;;;0CAGlD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU;gCACV,MAAK;gCACL,SAAQ;;oCAEP,0BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAE1B,YAAY,mBAAmB;;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;;;;;;;;;;;;;0CAI9B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,cAAc,WAAW,GAAG,KAAK,CAAC,GAAG;wCAC5C,UAAU,CAAC,IAAM,iBAAiB,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK;wCACzD,WAAU;;;;;;;;;;;;4BAIb,eAAe,2BACd,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,OAAO;4CACpD,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQtD,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;8CACC,cAAA,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;4CAC/D,aAAa,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACpC,8OAAC;oDAAe,WAAU;8DACxB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAW,gBAAgB,YAAY,CAAC,MAAM,CAAC,QAAQ;;;;;;0EACvE,8OAAC;gEAAK,WAAU;0EAAkC,KAAK,IAAI;;;;;;;;;;;;mDAHtD;;;;;;;;;;;;;;;;8CASf,8OAAC;8CACE,aAAa,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACjC,8OAAC;4CAAqB,WAAU;;8DAC9B,8OAAC;oDAAG,WAAU;8DACZ,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAW,gBAAgB,YAAY,CAAC,YAAY,CAAC,QAAQ;;;;;;0EAC7E,8OAAC;gEAAK,WAAU;0EAAkC,OAAO,IAAI;;;;;;;;;;;;;;;;;gDAGhE,aAAa,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAQ,0BAC9C,8OAAC;wDAAmB,WAAU;kEAC3B,gBAAgB,0BACf,8OAAC;4DAAI,WAAU;sEAAwB;;;;;mEACrC,OAAO,MAAM,KAAK,qBACpB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,IAAI;;;;;;8EAEvB,8OAAC;oEAAI,WAAU;8EACZ,OAAO,QAAQ,CAAC,IAAI;;;;;;gEAEtB,OAAO,iBAAiB,IAAI,iCAC3B,8OAAC;oEAAI,WAAU;;wEACZ,OAAO,iBAAiB,CAAC,IAAI;wEAAC;;;;;;;;;;;;iFAKrC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,wNAAA,CAAA,kBAAe;oEAAC,WAAU;;;;;;gEAAyB;;;;;;;uDAnBjD;;;;;;2CARJ;;;;;;;;;;;;;;;;;;;;;kCAwCjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CAAC,WAAW,mBAAmB;;gDAClC,mJAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gDAAY;gDAAE;;;;;;;sDAErD,8OAAC;4CAAK,WAAU;;8DACd,8OAAC,8MAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,eAAe;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAI,WAAU;;wCAAgB;wCACpB,aAAa,MAAM;wCAAC;wCAAE,aAAa,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;YAQ5D,2BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}, {"offset": {"line": 2543, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/trip-planner/TripMap.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Badge } from '@/components/ui/Badge'\nimport RouteOptimizer from './RouteOptimizer'\nimport TravelTimeMatrix from './TravelTimeMatrix'\nimport { routeOptimizationService, type RouteWaypoint, type OptimizedRoute } from '@/lib/services/route-optimization-service'\nimport { \n  MapPinIcon, \n  PlusIcon, \n  TrashIcon, \n  SearchIcon,\n  MapIcon,\n  ListIcon,\n  NavigationIcon\n} from 'lucide-react'\nimport toast from 'react-hot-toast'\n\ninterface TripPin {\n  id: string\n  name: string\n  description?: string\n  latitude: number\n  longitude: number\n  category: 'accommodation' | 'restaurant' | 'attraction' | 'transport' | 'shopping' | 'other'\n  priority: 'high' | 'medium' | 'low'\n  visited: boolean\n  notes?: string\n  estimatedCost?: number\n  estimatedDuration?: number // in minutes\n}\n\ninterface TripMapProps {\n  tripId: string\n  destination: {\n    city?: string\n    country: string\n    latitude?: number\n    longitude?: number\n  }\n  pins: TripPin[]\n  onPinsChange: (pins: TripPin[]) => void\n  readonly?: boolean\n}\n\nconst categoryColors = {\n  accommodation: '#e74c3c',\n  restaurant: '#f39c12',\n  attraction: '#3498db',\n  transport: '#9b59b6',\n  shopping: '#2ecc71',\n  other: '#95a5a6'\n}\n\nconst categoryIcons = {\n  accommodation: '🏨',\n  restaurant: '🍽️',\n  attraction: '🎯',\n  transport: '🚗',\n  shopping: '🛍️',\n  other: '📍'\n}\n\nexport default function TripMap({ tripId, destination, pins, onPinsChange, readonly = false }: TripMapProps) {\n  const mapRef = useRef<HTMLDivElement>(null)\n  const mapInstanceRef = useRef<google.maps.Map | null>(null)\n  const markersRef = useRef<google.maps.Marker[]>([])\n  const [isLoaded, setIsLoaded] = useState(false)\n  const [isLoading, setIsLoading] = useState(true)\n  const [viewMode, setViewMode] = useState<'map' | 'list' | 'route' | 'travel-times'>('map')\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedPin, setSelectedPin] = useState<TripPin | null>(null)\n  const [showAddForm, setShowAddForm] = useState(false)\n  const [optimizedRoute, setOptimizedRoute] = useState<OptimizedRoute | null>(null)\n  const [newPin, setNewPin] = useState({\n    name: '',\n    description: '',\n    category: 'attraction' as TripPin['category'],\n    priority: 'medium' as TripPin['priority'],\n    notes: '',\n    estimatedCost: '',\n    estimatedDuration: ''\n  })\n\n  const loadGoogleMapsScript = (apiKey: string): Promise<void> => {\n    return new Promise((resolve, reject) => {\n      // Check if script already exists\n      if (document.querySelector('script[src*=\"maps.googleapis.com\"]')) {\n        resolve()\n        return\n      }\n\n      const script = document.createElement('script')\n      script.src = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,geometry`\n      script.async = true\n      script.defer = true\n\n      script.onload = () => {\n        console.log('Google Maps script loaded')\n        resolve()\n      }\n\n      script.onerror = (error) => {\n        console.error('Failed to load Google Maps script:', error)\n        reject(new Error('Failed to load Google Maps script'))\n      }\n\n      document.head.appendChild(script)\n    })\n  }\n\n  useEffect(() => {\n    console.log('TripMap component mounted')\n    console.log('Environment API key:', process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing')\n    initializeMap()\n  }, [])\n\n  useEffect(() => {\n    if (isLoaded && mapInstanceRef.current) {\n      updateMapMarkers()\n    }\n  }, [pins, isLoaded])\n\n  const initializeMap = async () => {\n    try {\n      const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY\n\n      if (!apiKey || apiKey.trim() === '') {\n        console.log('No Google Maps API key found')\n        setIsLoading(false)\n        return\n      }\n\n      console.log('Initializing Google Maps with API key:', apiKey.substring(0, 10) + '...')\n      console.log('API key length:', apiKey.length)\n\n      // Try direct script loading approach\n      if (typeof google === 'undefined' || !google.maps) {\n        console.log('Loading Google Maps API via script...')\n        await loadGoogleMapsScript(apiKey)\n      }\n\n      console.log('Google Maps API loaded successfully')\n\n      // Verify that google.maps is available\n      if (typeof google === 'undefined' || !google.maps) {\n        throw new Error('Google Maps API not properly loaded')\n      }\n\n      if (!mapRef.current) {\n        console.error('Map container ref not found')\n        return\n      }\n\n      // Default coordinates for Brisbane, Australia if no specific coordinates\n      let defaultLat = destination.latitude || -27.4698\n      let defaultLng = destination.longitude || 153.0251\n      let defaultZoom = 12\n\n      // If we have city and country but no coordinates, we'll geocode later\n      if (!destination.latitude && destination.city && destination.country) {\n        // Use a more general zoom for geocoding\n        defaultZoom = 10\n      }\n\n      console.log('Creating map with center:', { lat: defaultLat, lng: defaultLng })\n\n      const map = new google.maps.Map(mapRef.current, {\n        center: { lat: defaultLat, lng: defaultLng },\n        zoom: defaultZoom,\n        mapTypeControl: true,\n        streetViewControl: true,\n        fullscreenControl: true,\n        zoomControl: true,\n        styles: [\n          {\n            featureType: 'poi',\n            elementType: 'labels',\n            stylers: [{ visibility: 'on' }]\n          }\n        ]\n      })\n\n      console.log('Map created successfully')\n      mapInstanceRef.current = map\n\n      // Add click listener for adding new pins\n      if (!readonly) {\n        map.addListener('click', (event: google.maps.MapMouseEvent) => {\n          if (event.latLng) {\n            console.log('Map clicked at:', event.latLng.lat(), event.latLng.lng())\n            handleMapClick(event.latLng.lat(), event.latLng.lng())\n          }\n        })\n      }\n\n      // If no destination coordinates, try to geocode the destination\n      if (!destination.latitude && destination.city && destination.country) {\n        console.log('Geocoding destination:', destination.city, destination.country)\n        await geocodeDestination(map)\n      }\n\n      setIsLoaded(true)\n      console.log('Map initialization complete')\n    } catch (error) {\n      console.error('Error loading Google Maps:', error)\n      toast.error(`Failed to load map: ${error.message}`)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const geocodeDestination = async (map: google.maps.Map) => {\n    const geocoder = new google.maps.Geocoder()\n    const address = `${destination.city}, ${destination.country}`\n\n    console.log('Geocoding address:', address)\n\n    try {\n      const result = await geocoder.geocode({ address })\n      console.log('Geocoding result:', result)\n\n      if (result.results && result.results.length > 0) {\n        const location = result.results[0].geometry.location\n        const lat = location.lat()\n        const lng = location.lng()\n\n        console.log('Setting map center to:', { lat, lng })\n        map.setCenter({ lat, lng })\n        map.setZoom(12)\n\n        // Update destination coordinates if needed\n        if (!destination.latitude) {\n          destination.latitude = lat\n          destination.longitude = lng\n        }\n      } else {\n        console.warn('No geocoding results found for:', address)\n        toast.error(`Could not find location: ${address}`)\n      }\n    } catch (error) {\n      console.error('Geocoding failed:', error)\n      toast.error(`Failed to locate ${address}`)\n    }\n  }\n\n  const updateMapMarkers = () => {\n    if (!mapInstanceRef.current) return\n\n    // Clear existing markers\n    markersRef.current.forEach(marker => marker.setMap(null))\n    markersRef.current = []\n\n    // Add markers for each pin\n    pins.forEach(pin => {\n      const marker = new google.maps.Marker({\n        position: { lat: pin.latitude, lng: pin.longitude },\n        map: mapInstanceRef.current,\n        title: pin.name,\n        icon: {\n          url: `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(createCustomMarkerSVG(pin))}`,\n          scaledSize: new google.maps.Size(40, 40),\n          anchor: new google.maps.Point(20, 40)\n        }\n      })\n\n      // Add info window\n      const infoWindow = new google.maps.InfoWindow({\n        content: createInfoWindowContent(pin)\n      })\n\n      marker.addListener('click', () => {\n        // Close other info windows\n        markersRef.current.forEach(m => {\n          const iw = (m as any).infoWindow\n          if (iw) iw.close()\n        })\n        \n        infoWindow.open(mapInstanceRef.current, marker)\n        setSelectedPin(pin)\n      })\n\n      ;(marker as any).infoWindow = infoWindow\n      markersRef.current.push(marker)\n    })\n  }\n\n  const createCustomMarkerSVG = (pin: TripPin) => {\n    const color = categoryColors[pin.category]\n    const opacity = pin.visited ? 0.6 : 1\n    \n    return `\n      <svg width=\"40\" height=\"40\" viewBox=\"0 0 40 40\" xmlns=\"http://www.w3.org/2000/svg\">\n        <circle cx=\"20\" cy=\"20\" r=\"15\" fill=\"${color}\" opacity=\"${opacity}\" stroke=\"white\" stroke-width=\"2\"/>\n        <text x=\"20\" y=\"25\" text-anchor=\"middle\" font-size=\"12\" fill=\"white\">${categoryIcons[pin.category]}</text>\n      </svg>\n    `\n  }\n\n  const createInfoWindowContent = (pin: TripPin) => {\n    return `\n      <div style=\"max-width: 250px; padding: 8px;\">\n        <h3 style=\"margin: 0 0 8px 0; font-size: 16px; font-weight: bold;\">${pin.name}</h3>\n        ${pin.description ? `<p style=\"margin: 0 0 8px 0; font-size: 14px; color: #666;\">${pin.description}</p>` : ''}\n        <div style=\"display: flex; gap: 8px; margin-bottom: 8px;\">\n          <span style=\"background: ${categoryColors[pin.category]}; color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;\">\n            ${pin.category}\n          </span>\n          <span style=\"background: #f0f0f0; padding: 2px 6px; border-radius: 4px; font-size: 12px;\">\n            ${pin.priority} priority\n          </span>\n        </div>\n        ${pin.estimatedCost ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Est. Cost:</strong> $${pin.estimatedCost}</p>` : ''}\n        ${pin.estimatedDuration ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Duration:</strong> ${pin.estimatedDuration} min</p>` : ''}\n        ${pin.notes ? `<p style=\"margin: 4px 0; font-size: 12px;\"><strong>Notes:</strong> ${pin.notes}</p>` : ''}\n        ${!readonly ? `\n          <div style=\"margin-top: 8px; display: flex; gap: 8px;\">\n            <button onclick=\"window.editPin('${pin.id}')\" style=\"background: #3498db; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;\">\n              Edit\n            </button>\n            <button onclick=\"window.deletePin('${pin.id}')\" style=\"background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 4px; font-size: 12px; cursor: pointer;\">\n              Delete\n            </button>\n          </div>\n        ` : ''}\n      </div>\n    `\n  }\n\n  const handleMapClick = (lat: number, lng: number) => {\n    if (readonly) return\n    \n    // Set the coordinates for the new pin\n    setNewPin(prev => ({ ...prev, latitude: lat, longitude: lng }))\n    setShowAddForm(true)\n  }\n\n  const addPin = () => {\n    if (!newPin.name.trim()) {\n      toast.error('Please enter a name for the pin')\n      return\n    }\n\n    const pin: TripPin = {\n      id: Date.now().toString(),\n      name: newPin.name.trim(),\n      description: newPin.description.trim() || undefined,\n      latitude: (newPin as any).latitude,\n      longitude: (newPin as any).longitude,\n      category: newPin.category,\n      priority: newPin.priority,\n      visited: false,\n      notes: newPin.notes.trim() || undefined,\n      estimatedCost: newPin.estimatedCost ? parseFloat(newPin.estimatedCost) : undefined,\n      estimatedDuration: newPin.estimatedDuration ? parseInt(newPin.estimatedDuration) : undefined\n    }\n\n    onPinsChange([...pins, pin])\n    \n    // Reset form\n    setNewPin({\n      name: '',\n      description: '',\n      category: 'attraction',\n      priority: 'medium',\n      notes: '',\n      estimatedCost: '',\n      estimatedDuration: ''\n    })\n    setShowAddForm(false)\n    toast.success('Pin added successfully!')\n  }\n\n  const deletePin = (pinId: string) => {\n    onPinsChange(pins.filter(pin => pin.id !== pinId))\n    toast.success('Pin deleted successfully!')\n  }\n\n  const togglePinVisited = (pinId: string) => {\n    onPinsChange(pins.map(pin => \n      pin.id === pinId ? { ...pin, visited: !pin.visited } : pin\n    ))\n  }\n\n  // Expose functions to global scope for info window buttons\n  useEffect(() => {\n    ;(window as any).editPin = (pinId: string) => {\n      const pin = pins.find(p => p.id === pinId)\n      if (pin) {\n        setSelectedPin(pin)\n        // TODO: Implement edit functionality\n        toast.info('Edit functionality coming soon!')\n      }\n    }\n\n    ;(window as any).deletePin = (pinId: string) => {\n      if (confirm('Are you sure you want to delete this pin?')) {\n        deletePin(pinId)\n      }\n    }\n\n    return () => {\n      delete (window as any).editPin\n      delete (window as any).deletePin\n    }\n  }, [pins])\n\n  const filteredPins = pins.filter(pin =>\n    pin.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    pin.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    pin.category.toLowerCase().includes(searchQuery.toLowerCase())\n  )\n\n  const convertPinsToWaypoints = (pins: TripPin[]): RouteWaypoint[] => {\n    return pins.map(pin => ({\n      id: pin.id,\n      name: pin.name,\n      latitude: pin.latitude,\n      longitude: pin.longitude,\n      category: pin.category,\n      priority: pin.priority,\n      estimatedDuration: pin.estimatedDuration\n    }))\n  }\n\n  const handleRouteOptimized = (route: OptimizedRoute) => {\n    setOptimizedRoute(route)\n    // Optionally update the pins order based on optimized route\n    const reorderedPins = route.waypoints.map(wp =>\n      pins.find(pin => pin.id === wp.id)!\n    ).filter(Boolean)\n    onPinsChange(reorderedPins)\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center h-96 bg-gray-50 rounded-lg\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading map...</p>\n          <p className=\"text-xs text-gray-500 mt-2\">\n            API Key: {process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ? 'Present' : 'Missing'}\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show setup instructions if no API key\n  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {\n    return (\n      <div className=\"space-y-4\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div className=\"flex items-center space-x-4\">\n            <h3 className=\"text-lg font-semibold text-gray-900\">Trip Map</h3>\n            <Badge variant=\"secondary\">{pins.length} pins</Badge>\n          </div>\n        </div>\n\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n          <div className=\"flex items-start\">\n            <MapIcon className=\"h-6 w-6 text-yellow-600 mt-1 mr-3\" />\n            <div>\n              <h4 className=\"text-lg font-medium text-yellow-800 mb-2\">Google Maps Setup Required</h4>\n              <p className=\"text-yellow-700 mb-4\">\n                To use the interactive map feature with custom drop pins, you need to set up a Google Maps API key.\n              </p>\n              <div className=\"space-y-2 text-sm text-yellow-700\">\n                <p><strong>Steps to set up:</strong></p>\n                <ol className=\"list-decimal list-inside space-y-1 ml-4\">\n                  <li>Go to <a href=\"https://console.cloud.google.com/google/maps-apis/\" target=\"_blank\" rel=\"noopener noreferrer\" className=\"underline hover:text-yellow-800\">Google Cloud Console</a></li>\n                  <li>Create a new project or select an existing one</li>\n                  <li>Enable the \"Maps JavaScript API\" and \"Places API\"</li>\n                  <li>Create an API key in the \"Credentials\" section</li>\n                  <li>Add the API key to your .env.local file as NEXT_PUBLIC_GOOGLE_MAPS_API_KEY</li>\n                  <li>Restart your development server</li>\n                </ol>\n              </div>\n              <div className=\"mt-4\">\n                <Button\n                  onClick={() => window.open('https://console.cloud.google.com/google/maps-apis/', '_blank')}\n                  className=\"bg-yellow-600 hover:bg-yellow-700 text-white\"\n                >\n                  <NavigationIcon className=\"h-4 w-4 mr-2\" />\n                  Set Up Google Maps API\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Fallback List View */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h4 className=\"font-medium text-gray-900\">Places to Visit (List View)</h4>\n            <p className=\"text-sm text-gray-600 mt-1\">Add places manually below. Map view will be available once Google Maps is configured.</p>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {pins.map((pin) => (\n              <div key={pin.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-lg\">{categoryIcons[pin.category]}</span>\n                      <div>\n                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>\n                          {pin.name}\n                        </h5>\n                        {pin.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{pin.description}</p>\n                        )}\n                        <div className=\"flex items-center space-x-2 mt-2\">\n                          <Badge\n                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}\n                            className=\"text-xs\"\n                          >\n                            {pin.category}\n                          </Badge>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {pin.priority} priority\n                          </Badge>\n                          {pin.visited && (\n                            <Badge variant=\"secondary\" className=\"text-xs bg-green-100 text-green-800\">\n                              Visited\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {!readonly && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => togglePinVisited(pin.id)}\n                        className=\"text-xs\"\n                      >\n                        {pin.visited ? 'Unmark' : 'Mark Visited'}\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => deletePin(pin.id)}\n                        className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                      >\n                        <TrashIcon className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  )}\n                </div>\n\n                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (\n                  <div className=\"mt-3 text-sm text-gray-600 space-y-1\">\n                    {pin.estimatedCost && (\n                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>\n                    )}\n                    {pin.estimatedDuration && (\n                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>\n                    )}\n                    {pin.notes && (\n                      <p><strong>Notes:</strong> {pin.notes}</p>\n                    )}\n                  </div>\n                )}\n              </div>\n            ))}\n\n            {pins.length === 0 && (\n              <div className=\"p-8 text-center text-gray-500\">\n                <MapPinIcon className=\"mx-auto h-12 w-12 text-gray-300 mb-3\" />\n                <p className=\"text-sm font-medium\">No places added yet</p>\n                <p className=\"text-xs text-gray-400 mt-1\">Click \"Add Pin\" below to add places to visit</p>\n              </div>\n            )}\n          </div>\n\n          {!readonly && (\n            <div className=\"p-4 border-t border-gray-200\">\n              <Button\n                onClick={() => {\n                  // For fallback, we'll set dummy coordinates\n                  setNewPin(prev => ({ ...prev, latitude: 0, longitude: 0 }))\n                  setShowAddForm(true)\n                }}\n                className=\"w-full\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Add Place to Visit\n              </Button>\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Header Controls */}\n      <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n        <div className=\"flex items-center space-x-4\">\n          <h3 className=\"text-lg font-semibold text-gray-900\">Trip Map</h3>\n          <Badge variant=\"secondary\">{pins.length} pins</Badge>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          {/* Search */}\n          <div className=\"relative\">\n            <SearchIcon className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\n            <Input\n              placeholder=\"Search pins...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"pl-10 w-48\"\n            />\n          </div>\n          \n          {/* View Mode Toggle */}\n          <div className=\"flex items-center bg-gray-100 rounded-lg p-1\">\n            <Button\n              variant={viewMode === 'map' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('map')}\n              className=\"h-8\"\n            >\n              <MapIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'list' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('list')}\n              className=\"h-8\"\n            >\n              <ListIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'route' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('route')}\n              className=\"h-8\"\n            >\n              <NavigationIcon className=\"h-4 w-4\" />\n            </Button>\n            <Button\n              variant={viewMode === 'travel-times' ? 'default' : 'ghost'}\n              size=\"sm\"\n              onClick={() => setViewMode('travel-times')}\n              className=\"h-8\"\n            >\n              <ClockIcon className=\"h-4 w-4\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Map, List, or Route View */}\n      {viewMode === 'map' ? (\n        <div className=\"relative\">\n          <div\n            ref={mapRef}\n            className=\"w-full h-96 rounded-lg shadow-sm border border-gray-200 bg-gray-100\"\n            style={{ minHeight: '384px' }}\n          />\n\n          {!readonly && (\n            <div className=\"absolute top-4 right-4\">\n              <Button\n                onClick={() => setShowAddForm(true)}\n                className=\"bg-white shadow-lg hover:bg-gray-50 text-gray-700 border border-gray-200\"\n              >\n                <PlusIcon className=\"h-4 w-4 mr-2\" />\n                Add Pin\n              </Button>\n            </div>\n          )}\n        </div>\n      ) : viewMode === 'route' ? (\n        <RouteOptimizer\n          waypoints={convertPinsToWaypoints(pins)}\n          onRouteOptimized={handleRouteOptimized}\n        />\n      ) : viewMode === 'travel-times' ? (\n        <TravelTimeMatrix\n          destinations={pins.map(pin => ({\n            id: pin.id,\n            name: pin.name,\n            latitude: pin.latitude,\n            longitude: pin.longitude,\n            category: pin.category\n          }))}\n        />\n      ) : (\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"p-4 border-b border-gray-200\">\n            <h4 className=\"font-medium text-gray-900\">Places to Visit</h4>\n          </div>\n          <div className=\"divide-y divide-gray-200\">\n            {filteredPins.map((pin) => (\n              <div key={pin.id} className=\"p-4 hover:bg-gray-50\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-lg\">{categoryIcons[pin.category]}</span>\n                      <div>\n                        <h5 className={`font-medium ${pin.visited ? 'line-through text-gray-500' : 'text-gray-900'}`}>\n                          {pin.name}\n                        </h5>\n                        {pin.description && (\n                          <p className=\"text-sm text-gray-600 mt-1\">{pin.description}</p>\n                        )}\n                        <div className=\"flex items-center space-x-2 mt-2\">\n                          <Badge \n                            style={{ backgroundColor: categoryColors[pin.category], color: 'white' }}\n                            className=\"text-xs\"\n                          >\n                            {pin.category}\n                          </Badge>\n                          <Badge variant=\"secondary\" className=\"text-xs\">\n                            {pin.priority} priority\n                          </Badge>\n                          {pin.visited && (\n                            <Badge variant=\"secondary\" className=\"text-xs bg-green-100 text-green-800\">\n                              Visited\n                            </Badge>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {!readonly && (\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => togglePinVisited(pin.id)}\n                        className=\"text-xs\"\n                      >\n                        {pin.visited ? 'Unmark' : 'Mark Visited'}\n                      </Button>\n                      <Button\n                        size=\"sm\"\n                        variant=\"outline\"\n                        onClick={() => deletePin(pin.id)}\n                        className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                      >\n                        <TrashIcon className=\"h-3 w-3\" />\n                      </Button>\n                    </div>\n                  )}\n                </div>\n                \n                {(pin.estimatedCost || pin.estimatedDuration || pin.notes) && (\n                  <div className=\"mt-3 text-sm text-gray-600 space-y-1\">\n                    {pin.estimatedCost && (\n                      <p><strong>Est. Cost:</strong> ${pin.estimatedCost}</p>\n                    )}\n                    {pin.estimatedDuration && (\n                      <p><strong>Duration:</strong> {pin.estimatedDuration} minutes</p>\n                    )}\n                    {pin.notes && (\n                      <p><strong>Notes:</strong> {pin.notes}</p>\n                    )}\n                  </div>\n                )}\n              </div>\n            ))}\n            \n            {filteredPins.length === 0 && (\n              <div className=\"p-8 text-center text-gray-500\">\n                {searchQuery ? 'No pins match your search.' : 'No pins added yet. Click on the map to add places to visit!'}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Add Pin Modal */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n            <div className=\"p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Add New Pin</h3>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Name *\n                  </label>\n                  <Input\n                    value={newPin.name}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, name: e.target.value }))}\n                    placeholder=\"e.g., Eiffel Tower\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description\n                  </label>\n                  <Input\n                    value={newPin.description}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, description: e.target.value }))}\n                    placeholder=\"Brief description...\"\n                  />\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Category\n                    </label>\n                    <select\n                      value={newPin.category}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, category: e.target.value as TripPin['category'] }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"attraction\">Attraction</option>\n                      <option value=\"restaurant\">Restaurant</option>\n                      <option value=\"accommodation\">Accommodation</option>\n                      <option value=\"transport\">Transport</option>\n                      <option value=\"shopping\">Shopping</option>\n                      <option value=\"other\">Other</option>\n                    </select>\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Priority\n                    </label>\n                    <select\n                      value={newPin.priority}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, priority: e.target.value as TripPin['priority'] }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    >\n                      <option value=\"high\">High</option>\n                      <option value=\"medium\">Medium</option>\n                      <option value=\"low\">Low</option>\n                    </select>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Est. Cost ($)\n                    </label>\n                    <Input\n                      type=\"number\"\n                      value={newPin.estimatedCost}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedCost: e.target.value }))}\n                      placeholder=\"0\"\n                    />\n                  </div>\n                  \n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Duration (min)\n                    </label>\n                    <Input\n                      type=\"number\"\n                      value={newPin.estimatedDuration}\n                      onChange={(e) => setNewPin(prev => ({ ...prev, estimatedDuration: e.target.value }))}\n                      placeholder=\"60\"\n                    />\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Notes\n                  </label>\n                  <textarea\n                    value={newPin.notes}\n                    onChange={(e) => setNewPin(prev => ({ ...prev, notes: e.target.value }))}\n                    placeholder=\"Additional notes...\"\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n              \n              <div className=\"flex justify-end space-x-3 mt-6\">\n                <Button\n                  variant=\"outline\"\n                  onClick={() => setShowAddForm(false)}\n                >\n                  Cancel\n                </Button>\n                <Button onClick={addPin}>\n                  Add Pin\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAlBA;;;;;;;;;;AA+CA,MAAM,iBAAiB;IACrB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,UAAU;IACV,OAAO;AACT;AAEA,MAAM,gBAAgB;IACpB,eAAe;IACf,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,UAAU;IACV,OAAO;AACT;AAEe,SAAS,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,KAAK,EAAgB;IACzG,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IACtD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAwB,EAAE;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM;QACN,aAAa;QACb,UAAU;QACV,UAAU;QACV,OAAO;QACP,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,iCAAiC;YACjC,IAAI,SAAS,aAAa,CAAC,uCAAuC;gBAChE;gBACA;YACF;YAEA,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,GAAG,GAAG,CAAC,4CAA4C,EAAE,OAAO,0BAA0B,CAAC;YAC9F,OAAO,KAAK,GAAG;YACf,OAAO,KAAK,GAAG;YAEf,OAAO,MAAM,GAAG;gBACd,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,OAAO,OAAO,GAAG,CAAC;gBAChB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,OAAO,IAAI,MAAM;YACnB;YAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,wBAAwB,uCAA8C;QAClF;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,eAAe,OAAO,EAAE;YACtC;QACF;IACF,GAAG;QAAC;QAAM;KAAS;IAEnB,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM;YAEN,IAAI,CAAC,UAAU,OAAO,IAAI,OAAO,IAAI;gBACnC,QAAQ,GAAG,CAAC;gBACZ,aAAa;gBACb;YACF;YAEA,QAAQ,GAAG,CAAC,0CAA0C,OAAO,SAAS,CAAC,GAAG,MAAM;YAChF,QAAQ,GAAG,CAAC,mBAAmB,OAAO,MAAM;YAE5C,qCAAqC;YACrC,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,IAAI,EAAE;gBACjD,QAAQ,GAAG,CAAC;gBACZ,MAAM,qBAAqB;YAC7B;YAEA,QAAQ,GAAG,CAAC;YAEZ,uCAAuC;YACvC,IAAI,OAAO,WAAW,eAAe,CAAC,OAAO,IAAI,EAAE;gBACjD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,OAAO,OAAO,EAAE;gBACnB,QAAQ,KAAK,CAAC;gBACd;YACF;YAEA,yEAAyE;YACzE,IAAI,aAAa,YAAY,QAAQ,IAAI,CAAC;YAC1C,IAAI,aAAa,YAAY,SAAS,IAAI;YAC1C,IAAI,cAAc;YAElB,sEAAsE;YACtE,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,IAAI,IAAI,YAAY,OAAO,EAAE;gBACpE,wCAAwC;gBACxC,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,6BAA6B;gBAAE,KAAK;gBAAY,KAAK;YAAW;YAE5E,MAAM,MAAM,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,OAAO,EAAE;gBAC9C,QAAQ;oBAAE,KAAK;oBAAY,KAAK;gBAAW;gBAC3C,MAAM;gBACN,gBAAgB;gBAChB,mBAAmB;gBACnB,mBAAmB;gBACnB,aAAa;gBACb,QAAQ;oBACN;wBACE,aAAa;wBACb,aAAa;wBACb,SAAS;4BAAC;gCAAE,YAAY;4BAAK;yBAAE;oBACjC;iBACD;YACH;YAEA,QAAQ,GAAG,CAAC;YACZ,eAAe,OAAO,GAAG;YAEzB,yCAAyC;YACzC,IAAI,CAAC,UAAU;gBACb,IAAI,WAAW,CAAC,SAAS,CAAC;oBACxB,IAAI,MAAM,MAAM,EAAE;wBAChB,QAAQ,GAAG,CAAC,mBAAmB,MAAM,MAAM,CAAC,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG;wBACnE,eAAe,MAAM,MAAM,CAAC,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG;oBACrD;gBACF;YACF;YAEA,gEAAgE;YAChE,IAAI,CAAC,YAAY,QAAQ,IAAI,YAAY,IAAI,IAAI,YAAY,OAAO,EAAE;gBACpE,QAAQ,GAAG,CAAC,0BAA0B,YAAY,IAAI,EAAE,YAAY,OAAO;gBAC3E,MAAM,mBAAmB;YAC3B;YAEA,YAAY;YACZ,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,oBAAoB,EAAE,MAAM,OAAO,EAAE;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,MAAM,WAAW,IAAI,OAAO,IAAI,CAAC,QAAQ;QACzC,MAAM,UAAU,GAAG,YAAY,IAAI,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE;QAE7D,QAAQ,GAAG,CAAC,sBAAsB;QAElC,IAAI;YACF,MAAM,SAAS,MAAM,SAAS,OAAO,CAAC;gBAAE;YAAQ;YAChD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,GAAG;gBAC/C,MAAM,WAAW,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ;gBACpD,MAAM,MAAM,SAAS,GAAG;gBACxB,MAAM,MAAM,SAAS,GAAG;gBAExB,QAAQ,GAAG,CAAC,0BAA0B;oBAAE;oBAAK;gBAAI;gBACjD,IAAI,SAAS,CAAC;oBAAE;oBAAK;gBAAI;gBACzB,IAAI,OAAO,CAAC;gBAEZ,2CAA2C;gBAC3C,IAAI,CAAC,YAAY,QAAQ,EAAE;oBACzB,YAAY,QAAQ,GAAG;oBACvB,YAAY,SAAS,GAAG;gBAC1B;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,mCAAmC;gBAChD,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,yBAAyB,EAAE,SAAS;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS;QAC3C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,eAAe,OAAO,EAAE;QAE7B,yBAAyB;QACzB,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC;QACnD,WAAW,OAAO,GAAG,EAAE;QAEvB,2BAA2B;QAC3B,KAAK,OAAO,CAAC,CAAA;YACX,MAAM,SAAS,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC;gBACpC,UAAU;oBAAE,KAAK,IAAI,QAAQ;oBAAE,KAAK,IAAI,SAAS;gBAAC;gBAClD,KAAK,eAAe,OAAO;gBAC3B,OAAO,IAAI,IAAI;gBACf,MAAM;oBACJ,KAAK,CAAC,iCAAiC,EAAE,mBAAmB,sBAAsB,OAAO;oBACzF,YAAY,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;oBACrC,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;gBACpC;YACF;YAEA,kBAAkB;YAClB,MAAM,aAAa,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC;gBAC5C,SAAS,wBAAwB;YACnC;YAEA,OAAO,WAAW,CAAC,SAAS;gBAC1B,2BAA2B;gBAC3B,WAAW,OAAO,CAAC,OAAO,CAAC,CAAA;oBACzB,MAAM,KAAK,AAAC,EAAU,UAAU;oBAChC,IAAI,IAAI,GAAG,KAAK;gBAClB;gBAEA,WAAW,IAAI,CAAC,eAAe,OAAO,EAAE;gBACxC,eAAe;YACjB;YAEE,OAAe,UAAU,GAAG;YAC9B,WAAW,OAAO,CAAC,IAAI,CAAC;QAC1B;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,MAAM,QAAQ,cAAc,CAAC,IAAI,QAAQ,CAAC;QAC1C,MAAM,UAAU,IAAI,OAAO,GAAG,MAAM;QAEpC,OAAO,CAAC;;6CAEiC,EAAE,MAAM,WAAW,EAAE,QAAQ;6EACG,EAAE,aAAa,CAAC,IAAI,QAAQ,CAAC,CAAC;;IAEvG,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC;QAC/B,OAAO,CAAC;;2EAE+D,EAAE,IAAI,IAAI,CAAC;QAC9E,EAAE,IAAI,WAAW,GAAG,CAAC,4DAA4D,EAAE,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,GAAG;;mCAEnF,EAAE,cAAc,CAAC,IAAI,QAAQ,CAAC,CAAC;YACtD,EAAE,IAAI,QAAQ,CAAC;;;YAGf,EAAE,IAAI,QAAQ,CAAC;;;QAGnB,EAAE,IAAI,aAAa,GAAG,CAAC,wEAAwE,EAAE,IAAI,aAAa,CAAC,IAAI,CAAC,GAAG,GAAG;QAC9H,EAAE,IAAI,iBAAiB,GAAG,CAAC,sEAAsE,EAAE,IAAI,iBAAiB,CAAC,QAAQ,CAAC,GAAG,GAAG;QACxI,EAAE,IAAI,KAAK,GAAG,CAAC,mEAAmE,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG;QACzG,EAAE,CAAC,WAAW,CAAC;;6CAEsB,EAAE,IAAI,EAAE,CAAC;;;+CAGP,EAAE,IAAI,EAAE,CAAC;;;;QAIhD,CAAC,GAAG,GAAG;;IAEX,CAAC;IACH;IAEA,MAAM,iBAAiB,CAAC,KAAa;QACnC,IAAI,UAAU;QAEd,sCAAsC;QACtC,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;gBAAK,WAAW;YAAI,CAAC;QAC7D,eAAe;IACjB;IAEA,MAAM,SAAS;QACb,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI;YACvB,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,MAAe;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM,OAAO,IAAI,CAAC,IAAI;YACtB,aAAa,OAAO,WAAW,CAAC,IAAI,MAAM;YAC1C,UAAU,AAAC,OAAe,QAAQ;YAClC,WAAW,AAAC,OAAe,SAAS;YACpC,UAAU,OAAO,QAAQ;YACzB,UAAU,OAAO,QAAQ;YACzB,SAAS;YACT,OAAO,OAAO,KAAK,CAAC,IAAI,MAAM;YAC9B,eAAe,OAAO,aAAa,GAAG,WAAW,OAAO,aAAa,IAAI;YACzE,mBAAmB,OAAO,iBAAiB,GAAG,SAAS,OAAO,iBAAiB,IAAI;QACrF;QAEA,aAAa;eAAI;YAAM;SAAI;QAE3B,aAAa;QACb,UAAU;YACR,MAAM;YACN,aAAa;YACb,UAAU;YACV,UAAU;YACV,OAAO;YACP,eAAe;YACf,mBAAmB;QACrB;QACA,eAAe;QACf,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,YAAY,CAAC;QACjB,aAAa,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC3C,uJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa,KAAK,GAAG,CAAC,CAAA,MACpB,IAAI,EAAE,KAAK,QAAQ;gBAAE,GAAG,GAAG;gBAAE,SAAS,CAAC,IAAI,OAAO;YAAC,IAAI;IAE3D;IAEA,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;;QACN,OAAe,OAAO,GAAG,CAAC;YAC1B,MAAM,MAAM,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACpC,IAAI,KAAK;gBACP,eAAe;gBACf,qCAAqC;gBACrC,uJAAA,CAAA,UAAK,CAAC,IAAI,CAAC;YACb;QACF;QAEE,OAAe,SAAS,GAAG,CAAC;YAC5B,IAAI,QAAQ,8CAA8C;gBACxD,UAAU;YACZ;QACF;QAEA,OAAO;YACL,OAAO,AAAC,OAAe,OAAO;YAC9B,OAAO,AAAC,OAAe,SAAS;QAClC;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAC/B,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACvD,IAAI,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,OAC/D,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG7D,MAAM,yBAAyB,CAAC;QAC9B,OAAO,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;gBACtB,IAAI,IAAI,EAAE;gBACV,MAAM,IAAI,IAAI;gBACd,UAAU,IAAI,QAAQ;gBACtB,WAAW,IAAI,SAAS;gBACxB,UAAU,IAAI,QAAQ;gBACtB,UAAU,IAAI,QAAQ;gBACtB,mBAAmB,IAAI,iBAAiB;YAC1C,CAAC;IACH;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,4DAA4D;QAC5D,MAAM,gBAAgB,MAAM,SAAS,CAAC,GAAG,CAAC,CAAA,KACxC,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,GAAG,EAAE,GACjC,MAAM,CAAC;QACT,aAAa;IACf;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC;wBAAE,WAAU;;4BAA6B;4BAC9B,uCAA8C;;;;;;;;;;;;;;;;;;IAKlE;IAEA,wCAAwC;IACxC,uCAAkD;;IAmJlD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;;oCAAa,KAAK,MAAM;oCAAC;;;;;;;;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC,iIAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,QAAQ,YAAY;wCAC1C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,UAAU,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC,kNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,aAAa,iBAAiB,YAAY;wCACnD,MAAK;wCACL,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAEV,cAAA,8OAAC;4CAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5B,aAAa,sBACZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BAAE,WAAW;wBAAQ;;;;;;oBAG7B,CAAC,0BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,eAAe;4BAC9B,WAAU;;8CAEV,8OAAC,sMAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;uBAM3C,aAAa,wBACf,8OAAC,uJAAA,CAAA,UAAc;gBACb,WAAW,uBAAuB;gBAClC,kBAAkB;;;;;uBAElB,aAAa,+BACf,8OAAC,yJAAA,CAAA,UAAgB;gBACf,cAAc,KAAK,GAAG,CAAC,CAAA,MAAO,CAAC;wBAC7B,IAAI,IAAI,EAAE;wBACV,MAAM,IAAI,IAAI;wBACd,UAAU,IAAI,QAAQ;wBACtB,WAAW,IAAI,SAAS;wBACxB,UAAU,IAAI,QAAQ;oBACxB,CAAC;;;;;qCAGH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA4B;;;;;;;;;;;kCAE5C,8OAAC;wBAAI,WAAU;;4BACZ,aAAa,GAAG,CAAC,CAAC,oBACjB,8OAAC;oCAAiB,WAAU;;sDAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAW,aAAa,CAAC,IAAI,QAAQ,CAAC;;;;;;0EACtD,8OAAC;;kFACC,8OAAC;wEAAG,WAAW,CAAC,YAAY,EAAE,IAAI,OAAO,GAAG,+BAA+B,iBAAiB;kFACzF,IAAI,IAAI;;;;;;oEAEV,IAAI,WAAW,kBACd,8OAAC;wEAAE,WAAU;kFAA8B,IAAI,WAAW;;;;;;kFAE5D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFACJ,OAAO;oFAAE,iBAAiB,cAAc,CAAC,IAAI,QAAQ,CAAC;oFAAE,OAAO;gFAAQ;gFACvE,WAAU;0FAET,IAAI,QAAQ;;;;;;0FAEf,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;;oFAClC,IAAI,QAAQ;oFAAC;;;;;;;4EAEf,IAAI,OAAO,kBACV,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gDASpF,CAAC,0BACA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,iBAAiB,IAAI,EAAE;4DACtC,WAAU;sEAET,IAAI,OAAO,GAAG,WAAW;;;;;;sEAE5B,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,UAAU,IAAI,EAAE;4DAC/B,WAAU;sEAEV,cAAA,8OAAC,wMAAA,CAAA,YAAS;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wCAM5B,CAAC,IAAI,aAAa,IAAI,IAAI,iBAAiB,IAAI,IAAI,KAAK,mBACvD,8OAAC;4CAAI,WAAU;;gDACZ,IAAI,aAAa,kBAChB,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAmB;wDAAG,IAAI,aAAa;;;;;;;gDAEnD,IAAI,iBAAiB,kBACpB,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAkB;wDAAE,IAAI,iBAAiB;wDAAC;;;;;;;gDAEtD,IAAI,KAAK,kBACR,8OAAC;;sEAAE,8OAAC;sEAAO;;;;;;wDAAe;wDAAE,IAAI,KAAK;;;;;;;;;;;;;;mCA/DnC,IAAI,EAAE;;;;;4BAsEjB,aAAa,MAAM,KAAK,mBACvB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,+BAA+B;;;;;;;;;;;;;;;;;;YAQvD,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CAEzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,OAAO,IAAI;gDAClB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACrE,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,OAAO,WAAW;gDACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC5E,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,OAAO,QAAQ;wDACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAwB,CAAC;wDAChG,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAa;;;;;;0EAC3B,8OAAC;gEAAO,OAAM;0EAAgB;;;;;;0EAC9B,8OAAC;gEAAO,OAAM;0EAAY;;;;;;0EAC1B,8OAAC;gEAAO,OAAM;0EAAW;;;;;;0EACzB,8OAAC;gEAAO,OAAM;0EAAQ;;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,OAAO,OAAO,QAAQ;wDACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAwB,CAAC;wDAChG,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAO;;;;;;0EACrB,8OAAC;gEAAO,OAAM;0EAAS;;;;;;0EACvB,8OAAC;gEAAO,OAAM;0EAAM;;;;;;;;;;;;;;;;;;;;;;;;kDAK1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,OAAO,aAAa;wDAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAC9E,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC,iIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,OAAO,OAAO,iBAAiB;wDAC/B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDAClF,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,OAAO,KAAK;gDACnB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACtE,aAAY;gDACZ,MAAM;gDACN,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,eAAe;kDAC/B;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAS;kDAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzC", "debugId": null}}, {"offset": {"line": 3816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/components/test/GoogleMapsTest.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\nexport default function GoogleMapsTest() {\n  const [status, setStatus] = useState('Checking...')\n  const [apiKey, setApiKey] = useState('')\n  const [geocodeTest, setGeocodeTest] = useState('')\n\n  useEffect(() => {\n    const key = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY\n    setApiKey(key || 'Not found')\n\n    if (!key) {\n      setStatus('❌ API key not found')\n      return\n    }\n\n    // Test the API key with a simple geocoding request\n    testApiKey(key)\n\n    // Test if we can load the Google Maps API\n    const script = document.createElement('script')\n    script.src = `https://maps.googleapis.com/maps/api/js?key=${key}&libraries=places`\n    script.onload = () => {\n      setStatus('✅ Google Maps API loaded successfully')\n      console.log('Google Maps API loaded, testing map creation...')\n      testMapCreation()\n    }\n    script.onerror = (error) => {\n      console.error('Script loading error:', error)\n      setStatus('❌ Failed to load Google Maps API - Check console for details')\n    }\n\n    document.head.appendChild(script)\n\n    return () => {\n      try {\n        document.head.removeChild(script)\n      } catch (e) {\n        // Script might already be removed\n      }\n    }\n  }, [])\n\n  const testApiKey = async (key: string) => {\n    try {\n      const response = await fetch(\n        `https://maps.googleapis.com/maps/api/geocode/json?address=Brisbane,Australia&key=${key}`\n      )\n      const data = await response.json()\n\n      if (data.status === 'OK') {\n        setGeocodeTest('✅ Geocoding API working')\n      } else {\n        setGeocodeTest(`❌ Geocoding failed: ${data.status} - ${data.error_message || 'Unknown error'}`)\n      }\n    } catch (error) {\n      setGeocodeTest(`❌ Geocoding request failed: ${error.message}`)\n    }\n  }\n\n  const testMapCreation = () => {\n    try {\n      if (typeof google !== 'undefined' && google.maps) {\n        // Try to create a simple map\n        const mapDiv = document.createElement('div')\n        mapDiv.style.width = '100px'\n        mapDiv.style.height = '100px'\n        document.body.appendChild(mapDiv)\n\n        const map = new google.maps.Map(mapDiv, {\n          center: { lat: -27.4698, lng: 153.0251 },\n          zoom: 10\n        })\n\n        console.log('Map created successfully:', map)\n        setStatus('✅ Google Maps API and Map creation working')\n\n        // Clean up\n        document.body.removeChild(mapDiv)\n      } else {\n        setStatus('❌ Google Maps API loaded but google.maps not available')\n      }\n    } catch (error) {\n      console.error('Map creation error:', error)\n      setStatus(`❌ Map creation failed: ${error.message}`)\n    }\n  }\n\n  return (\n    <div className=\"p-4 bg-white border rounded-lg\">\n      <h3 className=\"font-semibold mb-2\">Google Maps API Test</h3>\n      <div className=\"space-y-2 text-sm\">\n        <p><strong>API Key:</strong> {apiKey.substring(0, 10)}...{apiKey.substring(apiKey.length - 4)}</p>\n        <p><strong>Key Length:</strong> {apiKey.length}</p>\n        <p><strong>Script Status:</strong> {status}</p>\n        <p><strong>Geocoding Test:</strong> {geocodeTest}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM;QACN,UAAU,OAAO;QAEjB,uCAAU;;QAGV;QAEA,mDAAmD;QACnD,WAAW;QAEX,0CAA0C;QAC1C,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG,CAAC,4CAA4C,EAAE,IAAI,iBAAiB,CAAC;QAClF,OAAO,MAAM,GAAG;YACd,UAAU;YACV,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,OAAO,OAAO,GAAG,CAAC;YAChB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,UAAU;QACZ;QAEA,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,OAAO;YACL,IAAI;gBACF,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,EAAE,OAAO,GAAG;YACV,kCAAkC;YACpC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,iFAAiF,EAAE,KAAK;YAE3F,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,MAAM,KAAK,MAAM;gBACxB,eAAe;YACjB,OAAO;gBACL,eAAe,CAAC,oBAAoB,EAAE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,aAAa,IAAI,iBAAiB;YAChG;QACF,EAAE,OAAO,OAAO;YACd,eAAe,CAAC,4BAA4B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,OAAO,WAAW,eAAe,OAAO,IAAI,EAAE;gBAChD,6BAA6B;gBAC7B,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,OAAO,KAAK,CAAC,KAAK,GAAG;gBACrB,OAAO,KAAK,CAAC,MAAM,GAAG;gBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,MAAM,MAAM,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ;oBACtC,QAAQ;wBAAE,KAAK,CAAC;wBAAS,KAAK;oBAAS;oBACvC,MAAM;gBACR;gBAEA,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,UAAU;gBAEV,WAAW;gBACX,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,UAAU,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;QACrD;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAqB;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAiB;4BAAE,OAAO,SAAS,CAAC,GAAG;4BAAI;4BAAI,OAAO,SAAS,CAAC,OAAO,MAAM,GAAG;;;;;;;kCAC3F,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAoB;4BAAE,OAAO,MAAM;;;;;;;kCAC9C,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAuB;4BAAE;;;;;;;kCACpC,8OAAC;;0CAAE,8OAAC;0CAAO;;;;;;4BAAwB;4BAAE;;;;;;;;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 4000, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/trips/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useParams } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { tripPlannerService, type Trip } from '@/lib/services/trip-planner-service'\nimport { Button } from '@/components/ui/Button'\nimport { Loading } from '@/components/ui/Loading'\nimport { Badge } from '@/components/ui/Badge'\nimport TripMap from '@/components/trip-planner/TripMap'\nimport TripCollaboration from '@/components/trip-planner/TripCollaboration'\nimport GoogleMapsTest from '@/components/test/GoogleMapsTest'\nimport { \n  MapPinIcon, \n  CalendarIcon, \n  DollarSignIcon, \n  UsersIcon,\n  ArrowLeftIcon,\n  EditIcon,\n  ShareIcon,\n  PlusIcon,\n  ClockIcon,\n  TrendingUpIcon\n} from 'lucide-react'\nimport Link from 'next/link'\nimport toast from 'react-hot-toast'\n\nexport default function TripDetailPage() {\n  const params = useParams()\n  const tripId = params.id as string\n  \n  const [trip, setTrip] = useState<Trip | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n  const [tripPins, setTripPins] = useState<any[]>([]) // TODO: Define proper type\n\n  useEffect(() => {\n    if (tripId) {\n      fetchTrip()\n    }\n  }, [tripId])\n\n  const fetchTrip = async () => {\n    try {\n      setLoading(true)\n      const data = await tripPlannerService.getTrip(tripId)\n      setTrip(data)\n    } catch (error) {\n      console.error('Error fetching trip:', error)\n      setError('Failed to load trip details')\n      toast.error('Failed to load trip details')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'planning': return 'bg-blue-100 text-blue-800'\n      case 'booked': return 'bg-green-100 text-green-800'\n      case 'in_progress': return 'bg-yellow-100 text-yellow-800'\n      case 'completed': return 'bg-gray-100 text-gray-800'\n      case 'cancelled': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getTripTypeColor = (type: string) => {\n    switch (type) {\n      case 'leisure': return 'bg-purple-100 text-purple-800'\n      case 'business': return 'bg-blue-100 text-blue-800'\n      case 'adventure': return 'bg-orange-100 text-orange-800'\n      case 'family': return 'bg-green-100 text-green-800'\n      case 'romantic': return 'bg-pink-100 text-pink-800'\n      case 'solo': return 'bg-indigo-100 text-indigo-800'\n      case 'group': return 'bg-yellow-100 text-yellow-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    })\n  }\n\n  const calculateDuration = () => {\n    if (!trip) return 0\n    const start = new Date(trip.start_date)\n    const end = new Date(trip.end_date)\n    const diffTime = Math.abs(end.getTime() - start.getTime())\n    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))\n  }\n\n  const budgetProgress = trip?.budget_total \n    ? Math.min((trip.budget_spent / trip.budget_total) * 100, 100)\n    : 0\n\n  if (loading) {\n    return <Loading size=\"lg\" text=\"Loading trip details...\" className=\"h-64\" />\n  }\n\n  if (error || !trip) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto\">\n          <h3 className=\"text-lg font-medium text-red-800 mb-2\">Trip Not Found</h3>\n          <p className=\"text-red-600 mb-4\">{error || 'The trip you are looking for does not exist.'}</p>\n          <Link href=\"/trips\">\n            <Button variant=\"outline\" className=\"text-red-600 border-red-300 hover:bg-red-50\">\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              Back to Trips\n            </Button>\n          </Link>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n      >\n        <div className=\"flex items-center space-x-4\">\n          <Link href=\"/trips\">\n            <Button variant=\"outline\" size=\"sm\">\n              <ArrowLeftIcon className=\"h-4 w-4 mr-2\" />\n              Back to Trips\n            </Button>\n          </Link>\n          <div>\n            <h1 className=\"text-3xl font-bold text-gray-900\">{trip.title}</h1>\n            <div className=\"flex items-center space-x-3 mt-2\">\n              <Badge className={getStatusColor(trip.status)}>\n                {trip.status.replace('_', ' ').toUpperCase()}\n              </Badge>\n              <Badge className={getTripTypeColor(trip.trip_type)}>\n                {trip.trip_type}\n              </Badge>\n              {trip.is_shared && (\n                <Badge className=\"bg-blue-100 text-blue-800\">\n                  <ShareIcon className=\"h-3 w-3 mr-1\" />\n                  Shared\n                </Badge>\n              )}\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Button variant=\"outline\">\n            <EditIcon className=\"h-4 w-4 mr-2\" />\n            Edit Trip\n          </Button>\n          <Button variant=\"outline\">\n            <ShareIcon className=\"h-4 w-4 mr-2\" />\n            Share\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Cover Image */}\n      {trip.cover_image_url && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.95 }}\n          animate={{ opacity: 1, scale: 1 }}\n          className=\"relative h-64 rounded-lg overflow-hidden\"\n        >\n          <img \n            src={trip.cover_image_url} \n            alt={trip.title}\n            className=\"w-full h-full object-cover\"\n          />\n          <div className=\"absolute inset-0 bg-black bg-opacity-20\" />\n        </motion.div>\n      )}\n\n      {/* Trip Overview */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.1 }}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n      >\n        {/* Destination */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-blue-100 p-2 rounded-lg\">\n              <MapPinIcon className=\"h-5 w-5 text-blue-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Destination</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">\n            {trip.destination_city ? `${trip.destination_city}, ` : ''}{trip.destination_country}\n          </p>\n        </div>\n\n        {/* Duration */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-green-100 p-2 rounded-lg\">\n              <ClockIcon className=\"h-5 w-5 text-green-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Duration</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">{calculateDuration()} days</p>\n          <p className=\"text-sm text-gray-500 mt-1\">\n            {formatDate(trip.start_date)} - {formatDate(trip.end_date)}\n          </p>\n        </div>\n\n        {/* Travelers */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-purple-100 p-2 rounded-lg\">\n              <UsersIcon className=\"h-5 w-5 text-purple-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Travelers</h3>\n          </div>\n          <p className=\"text-lg font-semibold text-gray-900\">{trip.traveler_count}</p>\n          <p className=\"text-sm text-gray-500 mt-1\">\n            {trip.traveler_count === 1 ? 'Solo trip' : 'Group trip'}\n          </p>\n        </div>\n\n        {/* Budget */}\n        <div className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\">\n          <div className=\"flex items-center mb-3\">\n            <div className=\"bg-yellow-100 p-2 rounded-lg\">\n              <DollarSignIcon className=\"h-5 w-5 text-yellow-600\" />\n            </div>\n            <h3 className=\"ml-3 text-sm font-medium text-gray-600\">Budget</h3>\n          </div>\n          {trip.budget_total ? (\n            <>\n              <p className=\"text-lg font-semibold text-gray-900\">\n                {new Intl.NumberFormat('en-US', {\n                  style: 'currency',\n                  currency: trip.currency,\n                }).format(trip.budget_spent)} / {new Intl.NumberFormat('en-US', {\n                  style: 'currency',\n                  currency: trip.currency,\n                }).format(trip.budget_total)}\n              </p>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 mt-2\">\n                <div \n                  className={`h-2 rounded-full ${\n                    budgetProgress > 90 ? 'bg-red-500' : \n                    budgetProgress > 75 ? 'bg-yellow-500' : 'bg-green-500'\n                  }`}\n                  style={{ width: `${budgetProgress}%` }}\n                />\n              </div>\n              <p className=\"text-sm text-gray-500 mt-1\">{budgetProgress.toFixed(1)}% used</p>\n            </>\n          ) : (\n            <p className=\"text-lg font-semibold text-gray-900\">No budget set</p>\n          )}\n        </div>\n      </motion.div>\n\n      {/* Description */}\n      {trip.description && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n          className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\"\n        >\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-3\">Description</h3>\n          <p className=\"text-gray-600 leading-relaxed\">{trip.description}</p>\n        </motion.div>\n      )}\n\n      {/* Google Maps Test */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.2 }}\n        className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\"\n      >\n        <GoogleMapsTest />\n      </motion.div>\n\n      {/* Trip Map */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.3 }}\n        className=\"bg-white p-6 rounded-lg shadow-sm border border-gray-200\"\n      >\n        <TripMap\n          tripId={tripId}\n          destination={{\n            city: trip.destination_city || undefined,\n            country: trip.destination_country,\n            // TODO: Add latitude/longitude to trip data\n          }}\n          pins={tripPins}\n          onPinsChange={setTripPins}\n        />\n      </motion.div>\n\n      {/* Quick Actions */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"grid grid-cols-1 md:grid-cols-3 gap-4\"\n      >\n        <Button className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <MapPinIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Destination</span>\n        </Button>\n        \n        <Button variant=\"outline\" className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <CalendarIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Activity</span>\n        </Button>\n        \n        <Button variant=\"outline\" className=\"h-16 flex flex-col items-center justify-center space-y-1\">\n          <DollarSignIcon className=\"h-5 w-5\" />\n          <span className=\"text-sm\">Add Expense</span>\n        </Button>\n      </motion.div>\n\n      {/* Coming Soon Sections */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.4 }}\n        className=\"bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center\"\n      >\n        <TrendingUpIcon className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">More Features Coming Soon!</h3>\n        <p className=\"text-gray-600 mb-4\">\n          Destinations, activities, expenses, itinerary planning, and more advanced features are being developed.\n        </p>\n        <div className=\"flex flex-wrap justify-center gap-2\">\n          <Badge variant=\"secondary\">Destinations</Badge>\n          <Badge variant=\"secondary\">Activities</Badge>\n          <Badge variant=\"secondary\">Expenses</Badge>\n          <Badge variant=\"secondary\">Itinerary</Badge>\n          <Badge variant=\"secondary\">Maps</Badge>\n          <Badge variant=\"secondary\">Weather</Badge>\n        </div>\n      </motion.div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AAzBA;;;;;;;;;;;;;;AA2Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,OAAO,EAAE;IAExB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE,EAAE,2BAA2B;;IAE/E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,MAAM,OAAO,MAAM,oJAAA,CAAA,qBAAkB,CAAC,OAAO,CAAC;YAC9C,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;YACT,uJAAA,CAAA,UAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,QAAQ,IAAI,KAAK,KAAK,UAAU;QACtC,MAAM,MAAM,IAAI,KAAK,KAAK,QAAQ;QAClC,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO;QACvD,OAAO,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;IAClD;IAEA,MAAM,iBAAiB,MAAM,eACzB,KAAK,GAAG,CAAC,AAAC,KAAK,YAAY,GAAG,KAAK,YAAY,GAAI,KAAK,OACxD;IAEJ,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAK,MAAK;YAA0B,WAAU;;;;;;IACrE;IAEA,IAAI,SAAS,CAAC,MAAM;QAClB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAAqB,SAAS;;;;;;kCAC3C,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,WAAU;;8CAClC,8OAAC,oNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;;sDAC7B,8OAAC,oNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;0CAI9C,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC,KAAK,KAAK;;;;;;kDAC5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,eAAe,KAAK,MAAM;0DACzC,KAAK,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;0DAE5C,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAW,iBAAiB,KAAK,SAAS;0DAC9C,KAAK,SAAS;;;;;;4CAEhB,KAAK,SAAS,kBACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;;kEACf,8OAAC,wMAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,+MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGvC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;;kDACd,8OAAC,wMAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;YAO3C,KAAK,eAAe,kBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,WAAU;;kCAEV,8OAAC;wBACC,KAAK,KAAK,eAAe;wBACzB,KAAK,KAAK,KAAK;wBACf,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAKnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;kDAExB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;;oCACV,KAAK,gBAAgB,GAAG,GAAG,KAAK,gBAAgB,CAAC,EAAE,CAAC,GAAG;oCAAI,KAAK,mBAAmB;;;;;;;;;;;;;kCAKxF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;;oCAAuC;oCAAoB;;;;;;;0CACxE,8OAAC;gCAAE,WAAU;;oCACV,WAAW,KAAK,UAAU;oCAAE;oCAAI,WAAW,KAAK,QAAQ;;;;;;;;;;;;;kCAK7D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wMAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;0CAEzD,8OAAC;gCAAE,WAAU;0CAAuC,KAAK,cAAc;;;;;;0CACvE,8OAAC;gCAAE,WAAU;0CACV,KAAK,cAAc,KAAK,IAAI,cAAc;;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;kDAE5B,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;;;;;;;4BAExD,KAAK,YAAY,iBAChB;;kDACE,8OAAC;wCAAE,WAAU;;4CACV,IAAI,KAAK,YAAY,CAAC,SAAS;gDAC9B,OAAO;gDACP,UAAU,KAAK,QAAQ;4CACzB,GAAG,MAAM,CAAC,KAAK,YAAY;4CAAE;4CAAI,IAAI,KAAK,YAAY,CAAC,SAAS;gDAC9D,OAAO;gDACP,UAAU,KAAK,QAAQ;4CACzB,GAAG,MAAM,CAAC,KAAK,YAAY;;;;;;;kDAE7B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAW,CAAC,iBAAiB,EAC3B,iBAAiB,KAAK,eACtB,iBAAiB,KAAK,kBAAkB,gBACxC;4CACF,OAAO;gDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAGzC,8OAAC;wCAAE,WAAU;;4CAA8B,eAAe,OAAO,CAAC;4CAAG;;;;;;;;6DAGvE,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;;;;;;;YAMxD,KAAK,WAAW,kBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAE,WAAU;kCAAiC,KAAK,WAAW;;;;;;;;;;;;0BAKlE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,8OAAC,4IAAA,CAAA,UAAc;;;;;;;;;;0BAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;0BAEV,cAAA,8OAAC,gJAAA,CAAA,UAAO;oBACN,QAAQ;oBACR,aAAa;wBACX,MAAM,KAAK,gBAAgB,IAAI;wBAC/B,SAAS,KAAK,mBAAmB;oBAEnC;oBACA,MAAM;oBACN,cAAc;;;;;;;;;;;0BAKlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,kIAAA,CAAA,SAAM;wBAAC,WAAU;;0CAChB,8OAAC,8MAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,8MAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;kCAG5B,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,WAAU;;0CAClC,8OAAC,sNAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;0CAC1B,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;0BAK9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,WAAU;;kCAEV,8OAAC,sNAAA,CAAA,iBAAc;wBAAC,WAAU;;;;;;kCAC1B,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;0CAC3B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAY;;;;;;;;;;;;;;;;;;;;;;;;AAKrC", "debugId": null}}]}