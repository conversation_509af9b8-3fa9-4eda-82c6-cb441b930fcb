import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { withSecurity } from '@/lib/security/middleware'
import { addSecurityHeaders } from '@/lib/security/utils'
import { rateLimitConfigs } from '@/lib/security/rate-limit'
import { handleSecureError, buildErrorContext } from '@/lib/security/error-handler'
import { ensureUserProfile } from '@/lib/auth/profile-manager'
import { getUserData, OptimizedQueryBuilder } from '@/lib/database/query-optimizer'
import { cacheQuery } from '@/lib/cache/cache-manager'

// GET /api/budget/categories - Get user's budget categories
export const GET = withSecurity(
  async (request: NextRequest, context: any) => {
    const errorContext = buildErrorContext(request, context.user?.id)

    try {
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      // Use optimized query with caching
      const { data: categories, error } = await getUserData(
        user.id,
        'budget_categories',
        {
          select: `
            id,
            name,
            type,
            budget_limit,
            color,
            icon,
            created_at,
            updated_at
          `,
          cache: true,
          cacheTTL: 300, // 5 minutes
          tags: [`user:${user.id}`, 'budget_categories'],
          orderBy: [{ column: 'name', ascending: true }]
        }
      )

      if (error) throw error

      const response = NextResponse.json({ categories: categories || [] })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error, 'Failed to fetch budget categories', errorContext)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)

// POST /api/budget/categories - Create new budget category
export const POST = withSecurity(
  async (request: NextRequest, context: any) => {
    const errorContext = buildErrorContext(request, context.user?.id)

    try {
      const requestData = await request.json()
      const { name, type, budget_limit, color, icon } = requestData

      // Enhanced validation
      if (!name || typeof name !== 'string' || name.length > 100) {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Invalid name' }
        }, { status: 400 })
      }

      if (!type || !['income', 'expense'].includes(type)) {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Type must be income or expense' }
        }, { status: 400 })
      }

      if (budget_limit !== undefined && (typeof budget_limit !== 'number' || budget_limit < 0)) {
        return NextResponse.json({
          error: { code: 'VALIDATION_ERROR', message: 'Budget limit must be a positive number' }
        }, { status: 400 })
      }

      const supabase = await createClient()
      const user = context.user

      // Ensure user profile exists
      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)

      const { data: category, error } = await supabase
        .from('budget_categories')
        .insert([
          {
            user_id: user.id,
            name: name.trim(),
            type,
            budget_limit: budget_limit || null,
            color: color || null,
            icon: icon || null
          },
        ])
        .select()
        .single()

      if (error) throw error

      // Invalidate cache for this user's budget categories
      const { cacheManager } = await import('@/lib/cache/cache-manager')
      await cacheManager.invalidateByTag(`user:${user.id}`)
      await cacheManager.invalidateByTag('budget_categories')

      const response = NextResponse.json({ category })
      return addSecurityHeaders(response)
    } catch (error) {
      return handleSecureError(error, 'Failed to create budget category', errorContext)
    }
  },
  {
    requireAuth: true,
    rateLimit: rateLimitConfigs.api,
    auditLog: true
  }
)
