{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/supabase/server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nexport async function createClient() {\n  const cookieStore = await cookies()\n\n  return createServerClient(\n    process.env.NEXT_PUBLIC_SUPABASE_URL!,\n    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,\n    {\n      cookies: {\n        getAll() {\n          return cookieStore.getAll()\n        },\n        setAll(cookiesToSet) {\n          try {\n            cookiesToSet.forEach(({ name, value, options }) =>\n              cookieStore.set(name, value, options)\n            )\n          } catch {\n            // The `setAll` method was called from a Server Component.\n            // This can be ignored if you have middleware refreshing\n            // user sessions.\n          }\n        },\n      },\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;AAEO,eAAe;IACpB,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,sUAGtB;QACE,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/config.ts"], "sourcesContent": ["/**\n * Security Configuration\n * Provides different security levels for development and production environments\n */\n\nexport interface SecurityConfig {\n  // Authentication settings\n  auth: {\n    requireEmailVerification: boolean\n    sessionTimeout: number // in milliseconds\n    maxFailedAttempts: number\n    lockoutDuration: number // in milliseconds\n    passwordMinLength: number\n    requireStrongPassword: boolean\n    enableMFA: boolean\n  }\n  \n  // Rate limiting settings\n  rateLimit: {\n    enabled: boolean\n    auth: {\n      requests: number\n      window: number // in milliseconds\n    }\n    api: {\n      requests: number\n      window: number\n    }\n    strict: {\n      requests: number\n      window: number\n    }\n  }\n  \n  // CORS settings\n  cors: {\n    enabled: boolean\n    allowedOrigins: string[]\n    allowedMethods: string[]\n    allowCredentials: boolean\n  }\n  \n  // Security headers\n  headers: {\n    enableCSP: boolean\n    enableHSTS: boolean\n    enableXFrameOptions: boolean\n    enableXContentTypeOptions: boolean\n    enableReferrerPolicy: boolean\n  }\n  \n  // Input validation\n  validation: {\n    strictMode: boolean\n    maxRequestSize: number // in bytes\n    sanitizeInput: boolean\n  }\n  \n  // Logging and monitoring\n  logging: {\n    enableAuditLog: boolean\n    logLevel: 'debug' | 'info' | 'warn' | 'error'\n    enableSecurityEvents: boolean\n  }\n  \n  // CSRF protection\n  csrf: {\n    enabled: boolean\n    cookieName: string\n    headerName: string\n  }\n}\n\n// Development configuration - relaxed security for easier development\nconst developmentConfig: SecurityConfig = {\n  auth: {\n    requireEmailVerification: false, // Disabled for easier testing\n    sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours\n    maxFailedAttempts: 10, // Higher threshold\n    lockoutDuration: 5 * 60 * 1000, // 5 minutes\n    passwordMinLength: 6, // Shorter for testing\n    requireStrongPassword: false,\n    enableMFA: false\n  },\n  \n  rateLimit: {\n    enabled: true,\n    auth: {\n      requests: 20, // More lenient\n      window: 15 * 60 * 1000 // 15 minutes\n    },\n    api: {\n      requests: 200, // Higher limits\n      window: 60 * 1000 // 1 minute\n    },\n    strict: {\n      requests: 50,\n      window: 60 * 1000\n    }\n  },\n  \n  cors: {\n    enabled: true,\n    allowedOrigins: ['http://localhost:3000', 'http://127.0.0.1:3000'],\n    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],\n    allowCredentials: true\n  },\n  \n  headers: {\n    enableCSP: false, // Disabled for easier development\n    enableHSTS: false, // Not needed for localhost\n    enableXFrameOptions: true,\n    enableXContentTypeOptions: true,\n    enableReferrerPolicy: true\n  },\n  \n  validation: {\n    strictMode: false,\n    maxRequestSize: 10 * 1024 * 1024, // 10MB\n    sanitizeInput: true\n  },\n  \n  logging: {\n    enableAuditLog: true,\n    logLevel: 'debug',\n    enableSecurityEvents: true\n  },\n  \n  csrf: {\n    enabled: false, // Disabled for API-first development\n    cookieName: '_csrf',\n    headerName: 'x-csrf-token'\n  }\n}\n\n// Production configuration - strict security\nconst productionConfig: SecurityConfig = {\n  auth: {\n    requireEmailVerification: true,\n    sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours\n    maxFailedAttempts: 5,\n    lockoutDuration: 30 * 60 * 1000, // 30 minutes\n    passwordMinLength: 12,\n    requireStrongPassword: true,\n    enableMFA: true\n  },\n  \n  rateLimit: {\n    enabled: true,\n    auth: {\n      requests: 5,\n      window: 15 * 60 * 1000 // 15 minutes\n    },\n    api: {\n      requests: 100,\n      window: 60 * 1000 // 1 minute\n    },\n    strict: {\n      requests: 20,\n      window: 60 * 1000\n    }\n  },\n  \n  cors: {\n    enabled: true,\n    allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],\n    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],\n    allowCredentials: true\n  },\n  \n  headers: {\n    enableCSP: true,\n    enableHSTS: true,\n    enableXFrameOptions: true,\n    enableXContentTypeOptions: true,\n    enableReferrerPolicy: true\n  },\n  \n  validation: {\n    strictMode: true,\n    maxRequestSize: 5 * 1024 * 1024, // 5MB\n    sanitizeInput: true\n  },\n  \n  logging: {\n    enableAuditLog: true,\n    logLevel: 'warn',\n    enableSecurityEvents: true\n  },\n  \n  csrf: {\n    enabled: true,\n    cookieName: '_csrf',\n    headerName: 'x-csrf-token'\n  }\n}\n\n// Get configuration based on environment with override capabilities\nexport function getSecurityConfig(): SecurityConfig {\n  const env = process.env.NODE_ENV || 'development'\n  const securityMode = process.env.SECURITY_MODE\n\n  // Base configuration\n  let config: SecurityConfig\n\n  if (env === 'production') {\n    config = { ...productionConfig }\n  } else {\n    config = { ...developmentConfig }\n  }\n\n  // Allow environment variable overrides\n  if (securityMode === 'strict') {\n    config = { ...productionConfig }\n  } else if (securityMode === 'relaxed') {\n    config = { ...developmentConfig }\n  }\n\n  // Individual feature overrides via environment variables\n  if (process.env.DISABLE_RATE_LIMITING === 'true') {\n    config.rateLimit.enabled = false\n  }\n\n  if (process.env.DISABLE_CSRF === 'true') {\n    config.csrf.enabled = false\n  }\n\n  if (process.env.DISABLE_EMAIL_VERIFICATION === 'true') {\n    config.auth.requireEmailVerification = false\n  }\n\n  if (process.env.ENABLE_DEBUG_LOGGING === 'true') {\n    config.logging.logLevel = 'debug'\n  }\n\n  if (process.env.CORS_ORIGINS) {\n    config.cors.allowedOrigins = process.env.CORS_ORIGINS.split(',')\n  }\n\n  return config\n}\n\n// Helper functions for security mode management\nexport function isProduction(): boolean {\n  return process.env.NODE_ENV === 'production'\n}\n\nexport function isDevelopment(): boolean {\n  return process.env.NODE_ENV === 'development'\n}\n\nexport function isStrictMode(): boolean {\n  return process.env.SECURITY_MODE === 'strict' || isProduction()\n}\n\nexport function isRelaxedMode(): boolean {\n  return process.env.SECURITY_MODE === 'relaxed' || (isDevelopment() && !isStrictMode())\n}\n\nexport function getSecurityLevel(): 'strict' | 'normal' | 'relaxed' {\n  if (isStrictMode()) return 'strict'\n  if (isRelaxedMode()) return 'relaxed'\n  return 'normal'\n}\n\n// Feature-specific checks\nexport function isRateLimitingEnabled(): boolean {\n  return securityConfig.rateLimit.enabled && process.env.DISABLE_RATE_LIMITING !== 'true'\n}\n\nexport function isCSRFProtectionEnabled(): boolean {\n  return securityConfig.csrf.enabled && process.env.DISABLE_CSRF !== 'true'\n}\n\nexport function isEmailVerificationRequired(): boolean {\n  return securityConfig.auth.requireEmailVerification && process.env.DISABLE_EMAIL_VERIFICATION !== 'true'\n}\n\nexport function isCORSEnabled(): boolean {\n  return securityConfig.cors.enabled\n}\n\nexport function isAuditLoggingEnabled(): boolean {\n  return securityConfig.logging.enableAuditLog\n}\n\n// Dynamic configuration updates\nexport function updateSecurityConfig(updates: Partial<SecurityConfig>): void {\n  Object.assign(securityConfig, updates)\n  console.log('Security configuration updated:', {\n    level: getSecurityLevel(),\n    timestamp: new Date().toISOString()\n  })\n}\n\n// Security mode switching (for development/testing)\nexport function switchToStrictMode(): void {\n  if (!isProduction()) {\n    process.env.SECURITY_MODE = 'strict'\n    updateSecurityConfig(productionConfig)\n    console.warn('Switched to strict security mode')\n  }\n}\n\nexport function switchToRelaxedMode(): void {\n  if (!isProduction()) {\n    process.env.SECURITY_MODE = 'relaxed'\n    updateSecurityConfig(developmentConfig)\n    console.warn('Switched to relaxed security mode')\n  }\n}\n\n// Configuration validation\nexport function validateSecurityConfig(): { isValid: boolean; errors: string[] } {\n  const errors: string[] = []\n  const config = securityConfig\n\n  // Validate rate limiting\n  if (config.rateLimit.enabled) {\n    if (config.rateLimit.auth.requests <= 0) {\n      errors.push('Auth rate limit requests must be positive')\n    }\n    if (config.rateLimit.api.requests <= 0) {\n      errors.push('API rate limit requests must be positive')\n    }\n  }\n\n  // Validate authentication\n  if (config.auth.sessionTimeout <= 0) {\n    errors.push('Session timeout must be positive')\n  }\n  if (config.auth.passwordMinLength < 1) {\n    errors.push('Password minimum length must be at least 1')\n  }\n\n  // Validate CORS\n  if (config.cors.enabled && config.cors.allowedOrigins.length === 0) {\n    errors.push('CORS enabled but no allowed origins specified')\n  }\n\n  // Production-specific validations\n  if (isProduction()) {\n    if (!config.headers.enableHSTS) {\n      errors.push('HSTS should be enabled in production')\n    }\n    if (!config.auth.requireEmailVerification) {\n      errors.push('Email verification should be required in production')\n    }\n    if (config.logging.logLevel === 'debug') {\n      errors.push('Debug logging should not be enabled in production')\n    }\n  }\n\n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Export current config (will be updated dynamically)\nexport const securityConfig = getSecurityConfig()\n\n// Log current security configuration on startup\nconsole.log('Security Configuration Loaded:', {\n  environment: process.env.NODE_ENV,\n  securityMode: process.env.SECURITY_MODE || 'default',\n  level: getSecurityLevel(),\n  features: {\n    rateLimiting: isRateLimitingEnabled(),\n    csrfProtection: isCSRFProtectionEnabled(),\n    emailVerification: isEmailVerificationRequired(),\n    cors: isCORSEnabled(),\n    auditLogging: isAuditLoggingEnabled()\n  }\n})\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;;;;;AAsED,sEAAsE;AACtE,MAAM,oBAAoC;IACxC,MAAM;QACJ,0BAA0B;QAC1B,gBAAgB,KAAK,KAAK,KAAK;QAC/B,mBAAmB;QACnB,iBAAiB,IAAI,KAAK;QAC1B,mBAAmB;QACnB,uBAAuB;QACvB,WAAW;IACb;IAEA,WAAW;QACT,SAAS;QACT,MAAM;YACJ,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK,aAAa;QACtC;QACA,KAAK;YACH,UAAU;YACV,QAAQ,KAAK,KAAK,WAAW;QAC/B;QACA,QAAQ;YACN,UAAU;YACV,QAAQ,KAAK;QACf;IACF;IAEA,MAAM;QACJ,SAAS;QACT,gBAAgB;YAAC;YAAyB;SAAwB;QAClE,gBAAgB;YAAC;YAAO;YAAQ;YAAO;YAAU;YAAS;SAAU;QACpE,kBAAkB;IACpB;IAEA,SAAS;QACP,WAAW;QACX,YAAY;QACZ,qBAAqB;QACrB,2BAA2B;QAC3B,sBAAsB;IACxB;IAEA,YAAY;QACV,YAAY;QACZ,gBAAgB,KAAK,OAAO;QAC5B,eAAe;IACjB;IAEA,SAAS;QACP,gBAAgB;QAChB,UAAU;QACV,sBAAsB;IACxB;IAEA,MAAM;QACJ,SAAS;QACT,YAAY;QACZ,YAAY;IACd;AACF;AAEA,6CAA6C;AAC7C,MAAM,mBAAmC;IACvC,MAAM;QACJ,0BAA0B;QAC1B,gBAAgB,IAAI,KAAK,KAAK;QAC9B,mBAAmB;QACnB,iBAAiB,KAAK,KAAK;QAC3B,mBAAmB;QACnB,uBAAuB;QACvB,WAAW;IACb;IAEA,WAAW;QACT,SAAS;QACT,MAAM;YACJ,UAAU;YACV,QAAQ,KAAK,KAAK,KAAK,aAAa;QACtC;QACA,KAAK;YACH,UAAU;YACV,QAAQ,KAAK,KAAK,WAAW;QAC/B;QACA,QAAQ;YACN,UAAU;YACV,QAAQ,KAAK;QACf;IACF;IAEA,MAAM;QACJ,SAAS;QACT,gBAAgB,QAAQ,GAAG,CAAC,eAAe,EAAE,MAAM,QAAQ,EAAE;QAC7D,gBAAgB;YAAC;YAAO;YAAQ;YAAO;YAAU;SAAQ;QACzD,kBAAkB;IACpB;IAEA,SAAS;QACP,WAAW;QACX,YAAY;QACZ,qBAAqB;QACrB,2BAA2B;QAC3B,sBAAsB;IACxB;IAEA,YAAY;QACV,YAAY;QACZ,gBAAgB,IAAI,OAAO;QAC3B,eAAe;IACjB;IAEA,SAAS;QACP,gBAAgB;QAChB,UAAU;QACV,sBAAsB;IACxB;IAEA,MAAM;QACJ,SAAS;QACT,YAAY;QACZ,YAAY;IACd;AACF;AAGO,SAAS;IACd,MAAM,MAAM,mDAAwB;IACpC,MAAM,eAAe,QAAQ,GAAG,CAAC,aAAa;IAE9C,qBAAqB;IACrB,IAAI;IAEJ,IAAI,QAAQ,cAAc;QACxB,SAAS;YAAE,GAAG,gBAAgB;QAAC;IACjC,OAAO;QACL,SAAS;YAAE,GAAG,iBAAiB;QAAC;IAClC;IAEA,uCAAuC;IACvC,IAAI,iBAAiB,UAAU;QAC7B,SAAS;YAAE,GAAG,gBAAgB;QAAC;IACjC,OAAO,IAAI,iBAAiB,WAAW;QACrC,SAAS;YAAE,GAAG,iBAAiB;QAAC;IAClC;IAEA,yDAAyD;IACzD,IAAI,QAAQ,GAAG,CAAC,qBAAqB,KAAK,QAAQ;QAChD,OAAO,SAAS,CAAC,OAAO,GAAG;IAC7B;IAEA,IAAI,QAAQ,GAAG,CAAC,YAAY,KAAK,QAAQ;QACvC,OAAO,IAAI,CAAC,OAAO,GAAG;IACxB;IAEA,IAAI,QAAQ,GAAG,CAAC,0BAA0B,KAAK,QAAQ;QACrD,OAAO,IAAI,CAAC,wBAAwB,GAAG;IACzC;IAEA,IAAI,QAAQ,GAAG,CAAC,oBAAoB,KAAK,QAAQ;QAC/C,OAAO,OAAO,CAAC,QAAQ,GAAG;IAC5B;IAEA,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE;QAC5B,OAAO,IAAI,CAAC,cAAc,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC;IAC9D;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,oDAAyB;AAClC;AAEO,SAAS;IACd,OAAO,oDAAyB;AAClC;AAEO,SAAS;IACd,OAAO,QAAQ,GAAG,CAAC,aAAa,KAAK,YAAY;AACnD;AAEO,SAAS;IACd,OAAO,QAAQ,GAAG,CAAC,aAAa,KAAK,aAAc,mBAAmB,CAAC;AACzE;AAEO,SAAS;IACd,IAAI,gBAAgB,OAAO;IAC3B,IAAI,iBAAiB,OAAO;IAC5B,OAAO;AACT;AAGO,SAAS;IACd,OAAO,eAAe,SAAS,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,qBAAqB,KAAK;AACnF;AAEO,SAAS;IACd,OAAO,eAAe,IAAI,CAAC,OAAO,IAAI,QAAQ,GAAG,CAAC,YAAY,KAAK;AACrE;AAEO,SAAS;IACd,OAAO,eAAe,IAAI,CAAC,wBAAwB,IAAI,QAAQ,GAAG,CAAC,0BAA0B,KAAK;AACpG;AAEO,SAAS;IACd,OAAO,eAAe,IAAI,CAAC,OAAO;AACpC;AAEO,SAAS;IACd,OAAO,eAAe,OAAO,CAAC,cAAc;AAC9C;AAGO,SAAS,qBAAqB,OAAgC;IACnE,OAAO,MAAM,CAAC,gBAAgB;IAC9B,QAAQ,GAAG,CAAC,mCAAmC;QAC7C,OAAO;QACP,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAGO,SAAS;IACd,IAAI,CAAC,gBAAgB;QACnB,QAAQ,GAAG,CAAC,aAAa,GAAG;QAC5B,qBAAqB;QACrB,QAAQ,IAAI,CAAC;IACf;AACF;AAEO,SAAS;IACd,IAAI,CAAC,gBAAgB;QACnB,QAAQ,GAAG,CAAC,aAAa,GAAG;QAC5B,qBAAqB;QACrB,QAAQ,IAAI,CAAC;IACf;AACF;AAGO,SAAS;IACd,MAAM,SAAmB,EAAE;IAC3B,MAAM,SAAS;IAEf,yBAAyB;IACzB,IAAI,OAAO,SAAS,CAAC,OAAO,EAAE;QAC5B,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,QAAQ,IAAI,GAAG;YACvC,OAAO,IAAI,CAAC;QACd;QACA,IAAI,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG;YACtC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,0BAA0B;IAC1B,IAAI,OAAO,IAAI,CAAC,cAAc,IAAI,GAAG;QACnC,OAAO,IAAI,CAAC;IACd;IACA,IAAI,OAAO,IAAI,CAAC,iBAAiB,GAAG,GAAG;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,OAAO,IAAI,CAAC,OAAO,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;QAClE,OAAO,IAAI,CAAC;IACd;IAEA,kCAAkC;IAClC,IAAI,gBAAgB;;IAUpB;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,MAAM,iBAAiB;AAE9B,gDAAgD;AAChD,QAAQ,GAAG,CAAC,kCAAkC;IAC5C,WAAW;IACX,cAAc,QAAQ,GAAG,CAAC,aAAa,IAAI;IAC3C,OAAO;IACP,UAAU;QACR,cAAc;QACd,gBAAgB;QAChB,mBAAmB;QACnB,MAAM;QACN,cAAc;IAChB;AACF", "debugId": null}}, {"offset": {"line": 475, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/rate-limit.ts"], "sourcesContent": ["// Enhanced rate limiting with sliding window and performance optimizations\n// In production, consider using Redis for distributed rate limiting\n\nimport { securityConfig } from './config'\n\ninterface RateLimitEntry {\n  count: number\n  resetTime: number\n  timestamps: number[] // For sliding window\n}\n\ninterface RateLimitCache {\n  store: Map<string, RateLimitEntry>\n  lastCleanup: number\n}\n\n// Use a more efficient cache structure\nconst rateLimitCache: RateLimitCache = {\n  store: new Map<string, RateLimitEntry>(),\n  lastCleanup: Date.now()\n}\n\nexport interface RateLimitResult {\n  success: boolean\n  retryAfter?: number\n}\n\nexport async function rateLimit(\n  identifier: string,\n  requests: number = 100,\n  windowMs: number | string = 3600000\n): Promise<RateLimitResult> {\n  if (!securityConfig.rateLimit.enabled) {\n    return { success: true }\n  }\n\n  const windowMilliseconds = typeof windowMs === 'string' ? parseTimeWindow(windowMs) : windowMs\n  const now = Date.now()\n\n  // Periodic cleanup to prevent memory leaks\n  if (now - rateLimitCache.lastCleanup > 300000) { // 5 minutes\n    cleanupOldEntries(now)\n    rateLimitCache.lastCleanup = now\n  }\n\n  const entry = rateLimitCache.store.get(identifier)\n\n  if (!entry) {\n    rateLimitCache.store.set(identifier, {\n      count: 1,\n      resetTime: now + windowMilliseconds,\n      timestamps: [now]\n    })\n    return { success: true }\n  }\n\n  // Sliding window algorithm - remove old timestamps\n  entry.timestamps = entry.timestamps.filter(timestamp =>\n    now - timestamp < windowMilliseconds\n  )\n\n  if (entry.timestamps.length >= requests) {\n    const oldestTimestamp = Math.min(...entry.timestamps)\n    const retryAfter = Math.ceil((oldestTimestamp + windowMilliseconds - now) / 1000)\n    return {\n      success: false,\n      retryAfter: Math.max(retryAfter, 1)\n    }\n  }\n\n  // Add current timestamp\n  entry.timestamps.push(now)\n  entry.count = entry.timestamps.length\n  entry.resetTime = now + windowMilliseconds\n\n  return { success: true }\n}\n\nfunction parseTimeWindow(window: string): number {\n  const match = window.match(/^(\\d+)([smhd])$/)\n  if (!match) return 3600000 // Default to 1 hour\n  \n  const value = parseInt(match[1])\n  const unit = match[2]\n  \n  switch (unit) {\n    case 's': return value * 1000\n    case 'm': return value * 60 * 1000\n    case 'h': return value * 60 * 60 * 1000\n    case 'd': return value * 24 * 60 * 60 * 1000\n    default: return 3600000\n  }\n}\n\nfunction cleanupOldEntries(now: number) {\n  // Clean up entries older than 24 hours or with no recent activity\n  const cutoff = now - (24 * 60 * 60 * 1000)\n  const maxEntries = 10000 // Prevent memory bloat\n\n  let entriesRemoved = 0\n  for (const [key, entry] of rateLimitCache.store.entries()) {\n    if (entry.resetTime < cutoff || entriesRemoved > maxEntries) {\n      rateLimitCache.store.delete(key)\n      entriesRemoved++\n    }\n  }\n\n  // If still too many entries, remove oldest ones\n  if (rateLimitCache.store.size > maxEntries) {\n    const entries = Array.from(rateLimitCache.store.entries())\n      .sort(([,a], [,b]) => a.resetTime - b.resetTime)\n\n    const toRemove = entries.slice(0, entries.length - maxEntries)\n    toRemove.forEach(([key]) => rateLimitCache.store.delete(key))\n  }\n}\n\n// Enhanced rate limit configurations using security config\nexport const rateLimitConfigs = {\n  // Authentication endpoints - use config values\n  auth: securityConfig.rateLimit.auth,\n\n  // API endpoints - use config values\n  api: securityConfig.rateLimit.api,\n\n  // Strict endpoints - use config values\n  strict: securityConfig.rateLimit.strict,\n\n  // File upload endpoints\n  upload: { requests: 10, window: 60 * 60 * 1000 }, // 1 hour\n\n  // Recipe import (external API calls)\n  import: { requests: 20, window: 60 * 60 * 1000 }, // 1 hour\n\n  // Chat/AI endpoints\n  chat: { requests: 50, window: 60 * 60 * 1000 }, // 1 hour\n\n  // Search endpoints\n  search: { requests: 200, window: 60 * 60 * 1000 } // 1 hour\n}\n\n// IP-based rate limiting for public endpoints\nexport async function rateLimitByIP(\n  request: Request,\n  config: { requests: number; window: string } = rateLimitConfigs.api\n): Promise<RateLimitResult> {\n  const ip = getClientIP(request)\n  return rateLimit(`ip:${ip}`, config.requests, config.window)\n}\n\n// User-based rate limiting for authenticated endpoints\nexport async function rateLimitByUser(\n  userId: string,\n  config: { requests: number; window: string } = rateLimitConfigs.api\n): Promise<RateLimitResult> {\n  return rateLimit(`user:${userId}`, config.requests, config.window)\n}\n\nfunction getClientIP(request: Request): string {\n  // Try to get IP from various headers\n  const forwarded = request.headers.get('x-forwarded-for')\n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n  \n  const realIP = request.headers.get('x-real-ip')\n  if (realIP) {\n    return realIP\n  }\n  \n  const cfConnectingIP = request.headers.get('cf-connecting-ip')\n  if (cfConnectingIP) {\n    return cfConnectingIP\n  }\n  \n  return 'unknown'\n}\n"], "names": [], "mappings": "AAAA,2EAA2E;AAC3E,oEAAoE;;;;;;;AAEpE;;AAaA,uCAAuC;AACvC,MAAM,iBAAiC;IACrC,OAAO,IAAI;IACX,aAAa,KAAK,GAAG;AACvB;AAOO,eAAe,UACpB,UAAkB,EAClB,WAAmB,GAAG,EACtB,WAA4B,OAAO;IAEnC,IAAI,CAAC,kIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,OAAO,EAAE;QACrC,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,MAAM,qBAAqB,OAAO,aAAa,WAAW,gBAAgB,YAAY;IACtF,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,MAAM,eAAe,WAAW,GAAG,QAAQ;QAC7C,kBAAkB;QAClB,eAAe,WAAW,GAAG;IAC/B;IAEA,MAAM,QAAQ,eAAe,KAAK,CAAC,GAAG,CAAC;IAEvC,IAAI,CAAC,OAAO;QACV,eAAe,KAAK,CAAC,GAAG,CAAC,YAAY;YACnC,OAAO;YACP,WAAW,MAAM;YACjB,YAAY;gBAAC;aAAI;QACnB;QACA,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,mDAAmD;IACnD,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,CAAC,CAAA,YACzC,MAAM,YAAY;IAGpB,IAAI,MAAM,UAAU,CAAC,MAAM,IAAI,UAAU;QACvC,MAAM,kBAAkB,KAAK,GAAG,IAAI,MAAM,UAAU;QACpD,MAAM,aAAa,KAAK,IAAI,CAAC,CAAC,kBAAkB,qBAAqB,GAAG,IAAI;QAC5E,OAAO;YACL,SAAS;YACT,YAAY,KAAK,GAAG,CAAC,YAAY;QACnC;IACF;IAEA,wBAAwB;IACxB,MAAM,UAAU,CAAC,IAAI,CAAC;IACtB,MAAM,KAAK,GAAG,MAAM,UAAU,CAAC,MAAM;IACrC,MAAM,SAAS,GAAG,MAAM;IAExB,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,SAAS,gBAAgB,MAAc;IACrC,MAAM,QAAQ,OAAO,KAAK,CAAC;IAC3B,IAAI,CAAC,OAAO,OAAO,QAAQ,oBAAoB;;IAE/C,MAAM,QAAQ,SAAS,KAAK,CAAC,EAAE;IAC/B,MAAM,OAAO,KAAK,CAAC,EAAE;IAErB,OAAQ;QACN,KAAK;YAAK,OAAO,QAAQ;QACzB,KAAK;YAAK,OAAO,QAAQ,KAAK;QAC9B,KAAK;YAAK,OAAO,QAAQ,KAAK,KAAK;QACnC,KAAK;YAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;QACxC;YAAS,OAAO;IAClB;AACF;AAEA,SAAS,kBAAkB,GAAW;IACpC,kEAAkE;IAClE,MAAM,SAAS,MAAO,KAAK,KAAK,KAAK;IACrC,MAAM,aAAa,MAAM,uBAAuB;;IAEhD,IAAI,iBAAiB;IACrB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,eAAe,KAAK,CAAC,OAAO,GAAI;QACzD,IAAI,MAAM,SAAS,GAAG,UAAU,iBAAiB,YAAY;YAC3D,eAAe,KAAK,CAAC,MAAM,CAAC;YAC5B;QACF;IACF;IAEA,gDAAgD;IAChD,IAAI,eAAe,KAAK,CAAC,IAAI,GAAG,YAAY;QAC1C,MAAM,UAAU,MAAM,IAAI,CAAC,eAAe,KAAK,CAAC,OAAO,IACpD,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,EAAE,SAAS,GAAG,EAAE,SAAS;QAEjD,MAAM,WAAW,QAAQ,KAAK,CAAC,GAAG,QAAQ,MAAM,GAAG;QACnD,SAAS,OAAO,CAAC,CAAC,CAAC,IAAI,GAAK,eAAe,KAAK,CAAC,MAAM,CAAC;IAC1D;AACF;AAGO,MAAM,mBAAmB;IAC9B,+CAA+C;IAC/C,MAAM,kIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,IAAI;IAEnC,oCAAoC;IACpC,KAAK,kIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,GAAG;IAEjC,uCAAuC;IACvC,QAAQ,kIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,MAAM;IAEvC,wBAAwB;IACxB,QAAQ;QAAE,UAAU;QAAI,QAAQ,KAAK,KAAK;IAAK;IAE/C,qCAAqC;IACrC,QAAQ;QAAE,UAAU;QAAI,QAAQ,KAAK,KAAK;IAAK;IAE/C,oBAAoB;IACpB,MAAM;QAAE,UAAU;QAAI,QAAQ,KAAK,KAAK;IAAK;IAE7C,mBAAmB;IACnB,QAAQ;QAAE,UAAU;QAAK,QAAQ,KAAK,KAAK;IAAK,EAAE,SAAS;AAC7D;AAGO,eAAe,cACpB,OAAgB,EAChB,SAA+C,iBAAiB,GAAG;IAEnE,MAAM,KAAK,YAAY;IACvB,OAAO,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AAC7D;AAGO,eAAe,gBACpB,MAAc,EACd,SAA+C,iBAAiB,GAAG;IAEnE,OAAO,UAAU,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AACnE;AAEA,SAAS,YAAY,OAAgB;IACnC,qCAAqC;IACrC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC3C,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/validation.ts"], "sourcesContent": ["import { NextRequest } from 'next/server'\nimport { securityConfig } from './config'\n\nexport interface ValidationSchema {\n  [key: string]: {\n    required?: boolean\n    type?: 'string' | 'number' | 'boolean' | 'array' | 'object'\n    minLength?: number\n    maxLength?: number\n    min?: number\n    max?: number\n    pattern?: RegExp\n    enum?: string[]\n    maxItems?: number\n    custom?: (value: any) => boolean | string\n  }\n}\n\nexport interface ValidationResult {\n  success: boolean\n  errors?: string[]\n  data?: any\n}\n\nexport async function validateInput(\n  request: NextRequest,\n  schemas: { body?: ValidationSchema; query?: ValidationSchema }\n): Promise<ValidationResult> {\n  const errors: string[] = []\n  const validatedData: any = {}\n\n  try {\n    // Validate request body\n    if (schemas.body) {\n      let body: any = {}\n      \n      if (request.method !== 'GET' && request.method !== 'DELETE') {\n        try {\n          body = await request.json()\n        } catch (error) {\n          errors.push('Invalid JSON in request body')\n          return { success: false, errors }\n        }\n      }\n\n      const bodyValidation = validateObject(body, schemas.body, 'body')\n      if (!bodyValidation.success) {\n        errors.push(...bodyValidation.errors!)\n      } else {\n        validatedData.body = bodyValidation.data\n      }\n    }\n\n    // Validate query parameters\n    if (schemas.query) {\n      const searchParams = request.nextUrl.searchParams\n      const query: any = {}\n      \n      for (const [key, value] of searchParams.entries()) {\n        query[key] = value\n      }\n\n      const queryValidation = validateObject(query, schemas.query, 'query')\n      if (!queryValidation.success) {\n        errors.push(...queryValidation.errors!)\n      } else {\n        validatedData.query = queryValidation.data\n      }\n    }\n\n    return {\n      success: errors.length === 0,\n      errors: errors.length > 0 ? errors : undefined,\n      data: validatedData\n    }\n  } catch (error) {\n    console.error('Validation error:', error)\n    return {\n      success: false,\n      errors: ['Validation failed']\n    }\n  }\n}\n\nfunction validateObject(\n  obj: any,\n  schema: ValidationSchema,\n  prefix: string = ''\n): ValidationResult {\n  const errors: string[] = []\n  const validatedData: any = {}\n\n  for (const [key, rules] of Object.entries(schema)) {\n    const fieldPath = prefix ? `${prefix}.${key}` : key\n    const value = obj[key]\n\n    // Check required fields\n    if (rules.required && (value === undefined || value === null || value === '')) {\n      errors.push(`${fieldPath} is required`)\n      continue\n    }\n\n    // Skip validation if field is not required and not provided\n    if (!rules.required && (value === undefined || value === null)) {\n      continue\n    }\n\n    // Type validation\n    if (rules.type) {\n      const typeValidation = validateType(value, rules.type, fieldPath)\n      if (!typeValidation.success) {\n        errors.push(...typeValidation.errors!)\n        continue\n      }\n    }\n\n    // String validations\n    if (rules.type === 'string' && typeof value === 'string') {\n      if (rules.minLength && value.length < rules.minLength) {\n        errors.push(`${fieldPath} must be at least ${rules.minLength} characters`)\n        continue\n      }\n      \n      if (rules.maxLength && value.length > rules.maxLength) {\n        errors.push(`${fieldPath} must be no more than ${rules.maxLength} characters`)\n        continue\n      }\n      \n      if (rules.pattern && !rules.pattern.test(value)) {\n        errors.push(`${fieldPath} format is invalid`)\n        continue\n      }\n      \n      if (rules.enum && !rules.enum.includes(value)) {\n        errors.push(`${fieldPath} must be one of: ${rules.enum.join(', ')}`)\n        continue\n      }\n    }\n\n    // Number validations\n    if (rules.type === 'number' && typeof value === 'number') {\n      if (rules.min !== undefined && value < rules.min) {\n        errors.push(`${fieldPath} must be at least ${rules.min}`)\n        continue\n      }\n      \n      if (rules.max !== undefined && value > rules.max) {\n        errors.push(`${fieldPath} must be no more than ${rules.max}`)\n        continue\n      }\n    }\n\n    // Array validations\n    if (rules.type === 'array' && Array.isArray(value)) {\n      if (rules.maxItems && value.length > rules.maxItems) {\n        errors.push(`${fieldPath} must have no more than ${rules.maxItems} items`)\n        continue\n      }\n    }\n\n    // Custom validation\n    if (rules.custom) {\n      const customResult = rules.custom(value)\n      if (customResult !== true) {\n        errors.push(typeof customResult === 'string' ? customResult : `${fieldPath} is invalid`)\n        continue\n      }\n    }\n\n    // Sanitize and add to validated data\n    validatedData[key] = sanitizeValue(value, rules.type)\n  }\n\n  return {\n    success: errors.length === 0,\n    errors: errors.length > 0 ? errors : undefined,\n    data: validatedData\n  }\n}\n\nfunction validateType(value: any, expectedType: string, fieldPath: string): ValidationResult {\n  switch (expectedType) {\n    case 'string':\n      if (typeof value !== 'string') {\n        return { success: false, errors: [`${fieldPath} must be a string`] }\n      }\n      break\n    \n    case 'number':\n      if (typeof value !== 'number' || isNaN(value)) {\n        return { success: false, errors: [`${fieldPath} must be a number`] }\n      }\n      break\n    \n    case 'boolean':\n      if (typeof value !== 'boolean') {\n        return { success: false, errors: [`${fieldPath} must be a boolean`] }\n      }\n      break\n    \n    case 'array':\n      if (!Array.isArray(value)) {\n        return { success: false, errors: [`${fieldPath} must be an array`] }\n      }\n      break\n    \n    case 'object':\n      if (typeof value !== 'object' || value === null || Array.isArray(value)) {\n        return { success: false, errors: [`${fieldPath} must be an object`] }\n      }\n      break\n  }\n\n  return { success: true }\n}\n\nfunction sanitizeValue(value: any, type?: string): any {\n  if (value === null || value === undefined) {\n    return value\n  }\n\n  switch (type) {\n    case 'string':\n      let sanitized = typeof value === 'string' ? value : String(value)\n\n      if (securityConfig.validation.sanitizeInput) {\n        // Enhanced sanitization for security\n        sanitized = sanitizeForSQLInjection(sanitized)\n        sanitized = sanitizeForXSS(sanitized)\n        sanitized = sanitizeForPathTraversal(sanitized)\n      }\n\n      return sanitized.trim().slice(0, securityConfig.validation.maxRequestSize / 1000)\n\n    case 'number':\n      const num = typeof value === 'number' ? value : Number(value)\n      // Prevent extremely large numbers that could cause issues\n      return isFinite(num) ? Math.max(-1e15, Math.min(1e15, num)) : 0\n\n    case 'boolean':\n      return Boolean(value)\n\n    case 'array':\n      const arr = Array.isArray(value) ? value : [value]\n      return arr.slice(0, 1000).map(item => sanitizeValue(item, 'string'))\n\n    case 'object':\n      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {\n        const sanitizedObj: any = {}\n        for (const [key, val] of Object.entries(value)) {\n          if (typeof key === 'string' && key.length < 100) {\n            sanitizedObj[sanitizeValue(key, 'string')] = sanitizeValue(val)\n          }\n        }\n        return sanitizedObj\n      }\n      return {}\n\n    default:\n      return value\n  }\n}\n\n// SQL Injection prevention\nfunction sanitizeForSQLInjection(input: string): string {\n  if (!input) return input\n\n  // Common SQL injection patterns\n  const sqlPatterns = [\n    /(\\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\\b)/gi,\n    /(--|\\/\\*|\\*\\/|;|'|\"|`)/g,\n    /(\\bOR\\b|\\bAND\\b)\\s+\\d+\\s*=\\s*\\d+/gi,\n    /(\\bOR\\b|\\bAND\\b)\\s+['\"]?\\w+['\"]?\\s*=\\s*['\"]?\\w+['\"]?/gi,\n    /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/gi\n  ]\n\n  let sanitized = input\n  sqlPatterns.forEach(pattern => {\n    sanitized = sanitized.replace(pattern, '')\n  })\n\n  return sanitized\n}\n\n// XSS prevention\nfunction sanitizeForXSS(input: string): string {\n  if (!input) return input\n\n  return input\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;')\n    .replace(/\\//g, '&#x2F;')\n    .replace(/javascript:/gi, '')\n    .replace(/on\\w+\\s*=/gi, '')\n    .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n}\n\n// Path traversal prevention\nfunction sanitizeForPathTraversal(input: string): string {\n  if (!input) return input\n\n  return input\n    .replace(/\\.\\./g, '')\n    .replace(/[\\/\\\\]/g, '')\n    .replace(/\\0/g, '')\n}\n\n// Enhanced validation for security-critical fields\nexport function validateSecureField(value: string, fieldType: 'password' | 'email' | 'username' | 'filename'): { isValid: boolean; errors: string[] } {\n  const errors: string[] = []\n\n  if (!value || typeof value !== 'string') {\n    errors.push('Field is required and must be a string')\n    return { isValid: false, errors }\n  }\n\n  // Check for suspicious patterns\n  const suspiciousPatterns = [\n    /[\\x00-\\x1f\\x7f-\\x9f]/g, // Control characters\n    /[<>]/g, // HTML tags\n    /javascript:/gi, // JavaScript protocol\n    /data:/gi, // Data protocol\n    /vbscript:/gi, // VBScript protocol\n  ]\n\n  for (const pattern of suspiciousPatterns) {\n    if (pattern.test(value)) {\n      errors.push('Field contains invalid characters')\n      break\n    }\n  }\n\n  // Field-specific validation\n  switch (fieldType) {\n    case 'password':\n      if (securityConfig.auth.requireStrongPassword) {\n        if (value.length < securityConfig.auth.passwordMinLength) {\n          errors.push(`Password must be at least ${securityConfig.auth.passwordMinLength} characters`)\n        }\n        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]/.test(value)) {\n          errors.push('Password must contain uppercase, lowercase, number, and special character')\n        }\n      }\n      break\n\n    case 'email':\n      if (!patterns.email.test(value)) {\n        errors.push('Invalid email format')\n      }\n      break\n\n    case 'username':\n      if (!/^[a-zA-Z0-9_-]+$/.test(value)) {\n        errors.push('Username can only contain letters, numbers, underscores, and hyphens')\n      }\n      if (value.length < 3 || value.length > 30) {\n        errors.push('Username must be between 3 and 30 characters')\n      }\n      break\n\n    case 'filename':\n      if (!/^[a-zA-Z0-9._-]+$/.test(value)) {\n        errors.push('Filename contains invalid characters')\n      }\n      if (value.length > 255) {\n        errors.push('Filename too long')\n      }\n      break\n  }\n\n  return { isValid: errors.length === 0, errors }\n}\n\n// Common validation patterns\nexport const patterns = {\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  url: /^https?:\\/\\/.+/,\n  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,\n  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,\n  phone: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  date: /^\\d{4}-\\d{2}-\\d{2}$/,\n  time: /^\\d{2}:\\d{2}$/,\n  datetime: /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/\n}\n\n// Validation helpers for common use cases\nexport function validateEmail(email: string): boolean {\n  return patterns.email.test(email)\n}\n\nexport function validateURL(url: string): boolean {\n  return patterns.url.test(url)\n}\n\nexport function validateUUID(uuid: string): boolean {\n  return patterns.uuid.test(uuid)\n}\n\nexport function sanitizeHTML(input: string): string {\n  return input\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;')\n    .replace(/\\//g, '&#x2F;')\n}\n\nexport function validateAndSanitizeInput(\n  input: any,\n  schema: ValidationSchema\n): { isValid: boolean; sanitized: any; errors: string[] } {\n  const validation = validateObject(input, schema)\n  \n  return {\n    isValid: validation.success,\n    sanitized: validation.data || {},\n    errors: validation.errors || []\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;;AAuBO,eAAe,cACpB,OAAoB,EACpB,OAA8D;IAE9D,MAAM,SAAmB,EAAE;IAC3B,MAAM,gBAAqB,CAAC;IAE5B,IAAI;QACF,wBAAwB;QACxB,IAAI,QAAQ,IAAI,EAAE;YAChB,IAAI,OAAY,CAAC;YAEjB,IAAI,QAAQ,MAAM,KAAK,SAAS,QAAQ,MAAM,KAAK,UAAU;gBAC3D,IAAI;oBACF,OAAO,MAAM,QAAQ,IAAI;gBAC3B,EAAE,OAAO,OAAO;oBACd,OAAO,IAAI,CAAC;oBACZ,OAAO;wBAAE,SAAS;wBAAO;oBAAO;gBAClC;YACF;YAEA,MAAM,iBAAiB,eAAe,MAAM,QAAQ,IAAI,EAAE;YAC1D,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,IAAI,IAAI,eAAe,MAAM;YACtC,OAAO;gBACL,cAAc,IAAI,GAAG,eAAe,IAAI;YAC1C;QACF;QAEA,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,EAAE;YACjB,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;YACjD,MAAM,QAAa,CAAC;YAEpB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,aAAa,OAAO,GAAI;gBACjD,KAAK,CAAC,IAAI,GAAG;YACf;YAEA,MAAM,kBAAkB,eAAe,OAAO,QAAQ,KAAK,EAAE;YAC7D,IAAI,CAAC,gBAAgB,OAAO,EAAE;gBAC5B,OAAO,IAAI,IAAI,gBAAgB,MAAM;YACvC,OAAO;gBACL,cAAc,KAAK,GAAG,gBAAgB,IAAI;YAC5C;QACF;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;YACrC,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO;YACL,SAAS;YACT,QAAQ;gBAAC;aAAoB;QAC/B;IACF;AACF;AAEA,SAAS,eACP,GAAQ,EACR,MAAwB,EACxB,SAAiB,EAAE;IAEnB,MAAM,SAAmB,EAAE;IAC3B,MAAM,gBAAqB,CAAC;IAE5B,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,QAAS;QACjD,MAAM,YAAY,SAAS,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG;QAChD,MAAM,QAAQ,GAAG,CAAC,IAAI;QAEtB,wBAAwB;QACxB,IAAI,MAAM,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,QAAQ,UAAU,EAAE,GAAG;YAC7E,OAAO,IAAI,CAAC,GAAG,UAAU,YAAY,CAAC;YACtC;QACF;QAEA,4DAA4D;QAC5D,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,UAAU,aAAa,UAAU,IAAI,GAAG;YAC9D;QACF;QAEA,kBAAkB;QAClB,IAAI,MAAM,IAAI,EAAE;YACd,MAAM,iBAAiB,aAAa,OAAO,MAAM,IAAI,EAAE;YACvD,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,IAAI,IAAI,eAAe,MAAM;gBACpC;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,UAAU,UAAU;YACxD,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;gBACrD,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;gBACzE;YACF;YAEA,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;gBACrD,OAAO,IAAI,CAAC,GAAG,UAAU,sBAAsB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;gBAC7E;YACF;YAEA,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;gBAC/C,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,CAAC;gBAC5C;YACF;YAEA,IAAI,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBAC7C,OAAO,IAAI,CAAC,GAAG,UAAU,iBAAiB,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO;gBACnE;YACF;QACF;QAEA,qBAAqB;QACrB,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,UAAU,UAAU;YACxD,IAAI,MAAM,GAAG,KAAK,aAAa,QAAQ,MAAM,GAAG,EAAE;gBAChD,OAAO,IAAI,CAAC,GAAG,UAAU,kBAAkB,EAAE,MAAM,GAAG,EAAE;gBACxD;YACF;YAEA,IAAI,MAAM,GAAG,KAAK,aAAa,QAAQ,MAAM,GAAG,EAAE;gBAChD,OAAO,IAAI,CAAC,GAAG,UAAU,sBAAsB,EAAE,MAAM,GAAG,EAAE;gBAC5D;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,CAAC,QAAQ;YAClD,IAAI,MAAM,QAAQ,IAAI,MAAM,MAAM,GAAG,MAAM,QAAQ,EAAE;gBACnD,OAAO,IAAI,CAAC,GAAG,UAAU,wBAAwB,EAAE,MAAM,QAAQ,CAAC,MAAM,CAAC;gBACzE;YACF;QACF;QAEA,oBAAoB;QACpB,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,eAAe,MAAM,MAAM,CAAC;YAClC,IAAI,iBAAiB,MAAM;gBACzB,OAAO,IAAI,CAAC,OAAO,iBAAiB,WAAW,eAAe,GAAG,UAAU,WAAW,CAAC;gBACvF;YACF;QACF;QAEA,qCAAqC;QACrC,aAAa,CAAC,IAAI,GAAG,cAAc,OAAO,MAAM,IAAI;IACtD;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B,QAAQ,OAAO,MAAM,GAAG,IAAI,SAAS;QACrC,MAAM;IACR;AACF;AAEA,SAAS,aAAa,KAAU,EAAE,YAAoB,EAAE,SAAiB;IACvE,OAAQ;QACN,KAAK;YACH,IAAI,OAAO,UAAU,UAAU;gBAC7B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,MAAM,QAAQ;gBAC7C,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,WAAW;gBAC9B,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,kBAAkB,CAAC;qBAAC;gBAAC;YACtE;YACA;QAEF,KAAK;YACH,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACzB,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,iBAAiB,CAAC;qBAAC;gBAAC;YACrE;YACA;QAEF,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,MAAM,OAAO,CAAC,QAAQ;gBACvE,OAAO;oBAAE,SAAS;oBAAO,QAAQ;wBAAC,GAAG,UAAU,kBAAkB,CAAC;qBAAC;gBAAC;YACtE;YACA;IACJ;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAEA,SAAS,cAAc,KAAU,EAAE,IAAa;IAC9C,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,OAAQ;QACN,KAAK;YACH,IAAI,YAAY,OAAO,UAAU,WAAW,QAAQ,OAAO;YAE3D,IAAI,kIAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,aAAa,EAAE;gBAC3C,qCAAqC;gBACrC,YAAY,wBAAwB;gBACpC,YAAY,eAAe;gBAC3B,YAAY,yBAAyB;YACvC;YAEA,OAAO,UAAU,IAAI,GAAG,KAAK,CAAC,GAAG,kIAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,cAAc,GAAG;QAE9E,KAAK;YACH,MAAM,MAAM,OAAO,UAAU,WAAW,QAAQ,OAAO;YACvD,0DAA0D;YAC1D,OAAO,SAAS,OAAO,KAAK,GAAG,CAAC,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ;QAEhE,KAAK;YACH,OAAO,QAAQ;QAEjB,KAAK;YACH,MAAM,MAAM,MAAM,OAAO,CAAC,SAAS,QAAQ;gBAAC;aAAM;YAClD,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,GAAG,CAAC,CAAA,OAAQ,cAAc,MAAM;QAE5D,KAAK;YACH,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,CAAC,MAAM,OAAO,CAAC,QAAQ;gBACxE,MAAM,eAAoB,CAAC;gBAC3B,KAAK,MAAM,CAAC,KAAK,IAAI,IAAI,OAAO,OAAO,CAAC,OAAQ;oBAC9C,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,GAAG,KAAK;wBAC/C,YAAY,CAAC,cAAc,KAAK,UAAU,GAAG,cAAc;oBAC7D;gBACF;gBACA,OAAO;YACT;YACA,OAAO,CAAC;QAEV;YACE,OAAO;IACX;AACF;AAEA,2BAA2B;AAC3B,SAAS,wBAAwB,KAAa;IAC5C,IAAI,CAAC,OAAO,OAAO;IAEnB,gCAAgC;IAChC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD;IAED,IAAI,YAAY;IAChB,YAAY,OAAO,CAAC,CAAA;QAClB,YAAY,UAAU,OAAO,CAAC,SAAS;IACzC;IAEA,OAAO;AACT;AAEA,iBAAiB;AACjB,SAAS,eAAe,KAAa;IACnC,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,MACJ,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO,UACf,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,uDAAuD;AACpE;AAEA,4BAA4B;AAC5B,SAAS,yBAAyB,KAAa;IAC7C,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,MACJ,OAAO,CAAC,SAAS,IACjB,OAAO,CAAC,WAAW,IACnB,OAAO,CAAC,OAAO;AACpB;AAGO,SAAS,oBAAoB,KAAa,EAAE,SAAyD;IAC1G,MAAM,SAAmB,EAAE;IAE3B,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU;QACvC,OAAO,IAAI,CAAC;QACZ,OAAO;YAAE,SAAS;YAAO;QAAO;IAClC;IAEA,gCAAgC;IAChC,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,WAAW,mBAAoB;QACxC,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACvB,OAAO,IAAI,CAAC;YACZ;QACF;IACF;IAEA,4BAA4B;IAC5B,OAAQ;QACN,KAAK;YACH,IAAI,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBAC7C,IAAI,MAAM,MAAM,GAAG,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBACxD,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBAC7F;gBACA,IAAI,CAAC,iEAAiE,IAAI,CAAC,QAAQ;oBACjF,OAAO,IAAI,CAAC;gBACd;YACF;YACA;QAEF,KAAK;YACH,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBAC/B,OAAO,IAAI,CAAC;YACd;YACA;QAEF,KAAK;YACH,IAAI,CAAC,mBAAmB,IAAI,CAAC,QAAQ;gBACnC,OAAO,IAAI,CAAC;YACd;YACA,IAAI,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,IAAI;gBACzC,OAAO,IAAI,CAAC;YACd;YACA;QAEF,KAAK;YACH,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ;gBACpC,OAAO,IAAI,CAAC;YACd;YACA,IAAI,MAAM,MAAM,GAAG,KAAK;gBACtB,OAAO,IAAI,CAAC;YACd;YACA;IACJ;IAEA,OAAO;QAAE,SAAS,OAAO,MAAM,KAAK;QAAG;IAAO;AAChD;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,KAAK;IACL,MAAM;IACN,MAAM;IACN,OAAO;IACP,MAAM;IACN,MAAM;IACN,UAAU;AACZ;AAGO,SAAS,cAAc,KAAa;IACzC,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC;AAC7B;AAEO,SAAS,YAAY,GAAW;IACrC,OAAO,SAAS,GAAG,CAAC,IAAI,CAAC;AAC3B;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,yBACd,KAAU,EACV,MAAwB;IAExB,MAAM,aAAa,eAAe,OAAO;IAEzC,OAAO;QACL,SAAS,WAAW,OAAO;QAC3B,WAAW,WAAW,IAAI,IAAI,CAAC;QAC/B,QAAQ,WAAW,MAAM,IAAI,EAAE;IACjC;AACF", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/error-handler.ts"], "sourcesContent": ["import { NextResponse, NextRequest } from 'next/server'\nimport { securityConfig } from './config'\nimport crypto from 'crypto'\n\nexport interface SecureError {\n  code: string\n  message: string\n  statusCode: number\n  severity?: 'low' | 'medium' | 'high' | 'critical'\n  category?: 'auth' | 'validation' | 'rate_limit' | 'database' | 'security' | 'system'\n}\n\nexport interface ErrorContext {\n  userId?: string\n  ip?: string\n  userAgent?: string\n  endpoint?: string\n  method?: string\n  timestamp?: string\n  requestId?: string\n  sessionId?: string\n}\n\n// Enhanced secure error mappings with severity and category\nconst ERROR_MAPPINGS: Record<string, SecureError> = {\n  // Authentication errors\n  'PGRST301': {\n    code: 'AUTH_REQUIRED',\n    message: 'Authentication required',\n    statusCode: 401,\n    severity: 'medium',\n    category: 'auth'\n  },\n  'PGRST302': {\n    code: 'FORBIDDEN',\n    message: 'Access denied',\n    statusCode: 403,\n    severity: 'high',\n    category: 'auth'\n  },\n  'AUTH_FAILED': {\n    code: 'AUTH_FAILED',\n    message: 'Authentication failed',\n    statusCode: 401,\n    severity: 'medium',\n    category: 'auth'\n  },\n  'SESSION_EXPIRED': {\n    code: 'SESSION_EXPIRED',\n    message: 'Session expired',\n    statusCode: 401,\n    severity: 'low',\n    category: 'auth'\n  },\n  'EMAIL_NOT_VERIFIED': {\n    code: 'EMAIL_NOT_VERIFIED',\n    message: 'Email verification required',\n    statusCode: 403,\n    severity: 'medium',\n    category: 'auth'\n  },\n  'ACCOUNT_SUSPENDED': {\n    code: 'ACCOUNT_SUSPENDED',\n    message: 'Account suspended',\n    statusCode: 403,\n    severity: 'high',\n    category: 'auth'\n  },\n\n  // Database constraint errors\n  '23503': {\n    code: 'INVALID_REFERENCE',\n    message: 'Invalid data reference',\n    statusCode: 400,\n    severity: 'low',\n    category: 'validation'\n  },\n  '23505': {\n    code: 'DUPLICATE_ENTRY',\n    message: 'Duplicate entry not allowed',\n    statusCode: 409,\n    severity: 'low',\n    category: 'validation'\n  },\n  '23514': {\n    code: 'VALIDATION_ERROR',\n    message: 'Data validation failed',\n    statusCode: 400,\n    severity: 'low',\n    category: 'validation'\n  },\n\n  // Rate limiting\n  'RATE_LIMIT': {\n    code: 'RATE_LIMIT',\n    message: 'Too many requests',\n    statusCode: 429,\n    severity: 'medium',\n    category: 'rate_limit'\n  },\n\n  // Security errors\n  'CSRF_ERROR': {\n    code: 'CSRF_ERROR',\n    message: 'CSRF token validation failed',\n    statusCode: 403,\n    severity: 'high',\n    category: 'security'\n  },\n  'SUSPICIOUS_REQUEST': {\n    code: 'SUSPICIOUS_REQUEST',\n    message: 'Request blocked',\n    statusCode: 403,\n    severity: 'high',\n    category: 'security'\n  },\n  'SQL_INJECTION_ATTEMPT': {\n    code: 'SECURITY_VIOLATION',\n    message: 'Request blocked',\n    statusCode: 403,\n    severity: 'critical',\n    category: 'security'\n  },\n  'XSS_ATTEMPT': {\n    code: 'SECURITY_VIOLATION',\n    message: 'Request blocked',\n    statusCode: 403,\n    severity: 'critical',\n    category: 'security'\n  },\n\n  // Validation errors\n  'VALIDATION_ERROR': {\n    code: 'VALIDATION_ERROR',\n    message: 'Invalid input data',\n    statusCode: 400,\n    severity: 'low',\n    category: 'validation'\n  },\n  'REQUEST_TOO_LARGE': {\n    code: 'REQUEST_TOO_LARGE',\n    message: 'Request size exceeds limit',\n    statusCode: 413,\n    severity: 'medium',\n    category: 'validation'\n  },\n  'INVALID_CONTENT_TYPE': {\n    code: 'INVALID_CONTENT_TYPE',\n    message: 'Invalid content type',\n    statusCode: 415,\n    severity: 'low',\n    category: 'validation'\n  },\n\n  // System errors\n  'INTERNAL_ERROR': {\n    code: 'INTERNAL_ERROR',\n    message: 'An unexpected error occurred',\n    statusCode: 500,\n    severity: 'high',\n    category: 'system'\n  },\n  'SERVICE_UNAVAILABLE': {\n    code: 'SERVICE_UNAVAILABLE',\n    message: 'Service temporarily unavailable',\n    statusCode: 503,\n    severity: 'high',\n    category: 'system'\n  },\n  'DATABASE_ERROR': {\n    code: 'DATABASE_ERROR',\n    message: 'Database operation failed',\n    statusCode: 500,\n    severity: 'high',\n    category: 'database'\n  },\n}\n\nexport function handleSecureError(\n  error: any,\n  customMessage?: string,\n  context?: ErrorContext\n): NextResponse {\n  const errorId = crypto.randomUUID()\n  const timestamp = new Date().toISOString()\n\n  // Determine the error type\n  let secureError: SecureError = determineErrorType(error)\n\n  // Override message if provided\n  if (customMessage) {\n    secureError = { ...secureError, message: customMessage }\n  }\n\n  // Enhanced error logging with context\n  const errorLog = {\n    errorId,\n    timestamp,\n    error: {\n      name: error.name,\n      message: error.message,\n      code: error.code,\n      stack: securityConfig.logging.logLevel === 'debug' ? error.stack : undefined,\n      details: error.details,\n    },\n    secureError,\n    context: {\n      userId: context?.userId,\n      ip: context?.ip,\n      userAgent: context?.userAgent,\n      endpoint: context?.endpoint,\n      method: context?.method,\n      requestId: context?.requestId,\n      sessionId: context?.sessionId,\n    },\n    severity: secureError.severity,\n    category: secureError.category,\n  }\n\n  // Log based on severity and configuration\n  logError(errorLog)\n\n  // Send alerts for critical errors\n  if (secureError.severity === 'critical') {\n    sendCriticalErrorAlert(errorLog)\n  }\n\n  // Prepare response\n  const responseBody: any = {\n    error: {\n      code: secureError.code,\n      message: secureError.message,\n      ...(securityConfig.logging.logLevel === 'debug' && { errorId })\n    }\n  }\n\n  // Add additional debug info in development\n  if (securityConfig.logging.logLevel === 'debug' && process.env.NODE_ENV === 'development') {\n    responseBody.debug = {\n      originalError: error.message,\n      timestamp,\n      category: secureError.category,\n      severity: secureError.severity\n    }\n  }\n\n  const response = NextResponse.json(responseBody, { status: secureError.statusCode })\n\n  // Add security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff')\n  response.headers.set('X-Frame-Options', 'DENY')\n\n  return response\n}\n\nfunction determineErrorType(error: any): SecureError {\n  // Check for exact code match\n  if (error.code && ERROR_MAPPINGS[error.code]) {\n    return ERROR_MAPPINGS[error.code]\n  }\n\n  // Check for error name match\n  if (error.name && ERROR_MAPPINGS[error.name]) {\n    return ERROR_MAPPINGS[error.name]\n  }\n\n  // Pattern matching for common error types\n  const message = error.message?.toLowerCase() || ''\n\n  if (message.includes('rate limit') || message.includes('too many requests')) {\n    return ERROR_MAPPINGS['RATE_LIMIT']\n  }\n\n  if (message.includes('validation') || message.includes('invalid')) {\n    return ERROR_MAPPINGS['VALIDATION_ERROR']\n  }\n\n  if (message.includes('auth') || message.includes('unauthorized')) {\n    return ERROR_MAPPINGS['AUTH_REQUIRED']\n  }\n\n  if (message.includes('forbidden') || message.includes('access denied')) {\n    return ERROR_MAPPINGS['PGRST302']\n  }\n\n  if (message.includes('duplicate') || message.includes('already exists')) {\n    return ERROR_MAPPINGS['23505']\n  }\n\n  // Security pattern detection\n  if (detectSQLInjection(message)) {\n    return ERROR_MAPPINGS['SQL_INJECTION_ATTEMPT']\n  }\n\n  if (detectXSSAttempt(message)) {\n    return ERROR_MAPPINGS['XSS_ATTEMPT']\n  }\n\n  // Database errors\n  if (message.includes('database') || message.includes('connection') || error.code?.startsWith('23')) {\n    return ERROR_MAPPINGS['DATABASE_ERROR']\n  }\n\n  // Default to internal error\n  return ERROR_MAPPINGS['INTERNAL_ERROR']\n}\n\nfunction detectSQLInjection(message: string): boolean {\n  const sqlPatterns = [\n    /union.*select/i,\n    /drop.*table/i,\n    /insert.*into/i,\n    /delete.*from/i,\n    /update.*set/i,\n    /exec.*xp_/i,\n    /information_schema/i,\n    /sysobjects/i,\n    /syscolumns/i\n  ]\n\n  return sqlPatterns.some(pattern => pattern.test(message))\n}\n\nfunction detectXSSAttempt(message: string): boolean {\n  const xssPatterns = [\n    /<script/i,\n    /javascript:/i,\n    /vbscript:/i,\n    /onload=/i,\n    /onerror=/i,\n    /onclick=/i,\n    /eval\\(/i,\n    /expression\\(/i\n  ]\n\n  return xssPatterns.some(pattern => pattern.test(message))\n}\n\nfunction logError(errorLog: any): void {\n  const { severity, category } = errorLog.secureError\n\n  // Determine log level based on severity\n  switch (severity) {\n    case 'critical':\n      console.error('🚨 CRITICAL ERROR:', errorLog)\n      break\n    case 'high':\n      console.error('❌ HIGH SEVERITY ERROR:', errorLog)\n      break\n    case 'medium':\n      console.warn('⚠️ MEDIUM SEVERITY ERROR:', errorLog)\n      break\n    case 'low':\n      if (securityConfig.logging.logLevel === 'debug') {\n        console.info('ℹ️ LOW SEVERITY ERROR:', errorLog)\n      }\n      break\n    default:\n      console.error('ERROR:', errorLog)\n  }\n\n  // Log to external monitoring service if configured\n  if (process.env.SENTRY_DSN && (severity === 'critical' || severity === 'high')) {\n    // In a real implementation, you would send to Sentry here\n    console.log('Would send to Sentry:', errorLog.errorId)\n  }\n\n  // Store in audit log for security events\n  if (category === 'security' || category === 'auth') {\n    storeSecurityAuditLog(errorLog)\n  }\n}\n\nfunction sendCriticalErrorAlert(errorLog: any): void {\n  // In production, this would send alerts via email, Slack, PagerDuty, etc.\n  console.error('🚨 CRITICAL ERROR ALERT 🚨', {\n    errorId: errorLog.errorId,\n    message: errorLog.secureError.message,\n    category: errorLog.secureError.category,\n    timestamp: errorLog.timestamp,\n    context: errorLog.context\n  })\n\n  // Example: Send to monitoring service\n  if (process.env.WEBHOOK_URL) {\n    // In a real implementation, you would send a webhook here\n    console.log('Would send webhook alert for:', errorLog.errorId)\n  }\n}\n\nfunction storeSecurityAuditLog(errorLog: any): void {\n  // In a real implementation, this would store in a secure audit log database\n  if (securityConfig.logging.enableAuditLog) {\n    console.log('SECURITY AUDIT LOG:', {\n      type: 'error',\n      errorId: errorLog.errorId,\n      category: errorLog.secureError.category,\n      severity: errorLog.secureError.severity,\n      userId: errorLog.context.userId,\n      ip: errorLog.context.ip,\n      endpoint: errorLog.context.endpoint,\n      timestamp: errorLog.timestamp\n    })\n  }\n}\n\n// Enhanced error creation functions\nexport function createValidationError(message: string, details?: any): Error {\n  const error = new Error(message)\n  error.name = 'VALIDATION_ERROR'\n  if (details) {\n    (error as any).details = details\n  }\n  return error\n}\n\nexport function createAuthError(message: string = 'Authentication required', code?: string): Error {\n  const error = new Error(message)\n  error.name = code || 'AUTH_REQUIRED'\n  return error\n}\n\nexport function createForbiddenError(message: string = 'Access denied'): Error {\n  const error = new Error(message)\n  error.name = 'FORBIDDEN'\n  return error\n}\n\nexport function createSecurityError(message: string, type: 'CSRF_ERROR' | 'SUSPICIOUS_REQUEST' | 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT'): Error {\n  const error = new Error(message)\n  error.name = type\n  return error\n}\n\nexport function createRateLimitError(retryAfter?: number): Error {\n  const error = new Error('Too many requests')\n  error.name = 'RATE_LIMIT'\n  if (retryAfter) {\n    (error as any).retryAfter = retryAfter\n  }\n  return error\n}\n\n// Error context builder\nexport function buildErrorContext(request?: NextRequest, userId?: string): ErrorContext {\n  if (!request) {\n    return {\n      timestamp: new Date().toISOString(),\n      requestId: crypto.randomUUID()\n    }\n  }\n\n  return {\n    userId,\n    ip: getClientIP(request),\n    userAgent: request.headers.get('user-agent') || undefined,\n    endpoint: request.nextUrl.pathname,\n    method: request.method,\n    timestamp: new Date().toISOString(),\n    requestId: crypto.randomUUID(),\n    sessionId: request.cookies.get('session')?.value\n  }\n}\n\nfunction getClientIP(request: NextRequest): string {\n  const forwarded = request.headers.get('x-forwarded-for')\n  if (forwarded) {\n    return forwarded.split(',')[0].trim()\n  }\n\n  const realIP = request.headers.get('x-real-ip')\n  if (realIP) {\n    return realIP\n  }\n\n  const cfConnectingIP = request.headers.get('cf-connecting-ip')\n  if (cfConnectingIP) {\n    return cfConnectingIP\n  }\n\n  return 'unknown'\n}\n\n// Utility function for API routes\nexport function withErrorHandling<T extends any[], R>(\n  handler: (...args: T) => Promise<R>,\n  context?: Partial<ErrorContext>\n) {\n  return async (...args: T): Promise<R> => {\n    try {\n      return await handler(...args)\n    } catch (error) {\n      throw handleSecureError(error, undefined, context as ErrorContext)\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAqBA,4DAA4D;AAC5D,MAAM,iBAA8C;IAClD,wBAAwB;IACxB,YAAY;QACV,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,eAAe;QACb,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,qBAAqB;QACnB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,6BAA6B;IAC7B,SAAS;QACP,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,gBAAgB;IAChB,cAAc;QACZ,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,kBAAkB;IAClB,cAAc;QACZ,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,sBAAsB;QACpB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,yBAAyB;QACvB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,eAAe;QACb,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,oBAAoB;IACpB,oBAAoB;QAClB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,qBAAqB;QACnB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,wBAAwB;QACtB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IAEA,gBAAgB;IAChB,kBAAkB;QAChB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,uBAAuB;QACrB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;IACA,kBAAkB;QAChB,MAAM;QACN,SAAS;QACT,YAAY;QACZ,UAAU;QACV,UAAU;IACZ;AACF;AAEO,SAAS,kBACd,KAAU,EACV,aAAsB,EACtB,OAAsB;IAEtB,MAAM,UAAU,qGAAA,CAAA,UAAM,CAAC,UAAU;IACjC,MAAM,YAAY,IAAI,OAAO,WAAW;IAExC,2BAA2B;IAC3B,IAAI,cAA2B,mBAAmB;IAElD,+BAA+B;IAC/B,IAAI,eAAe;QACjB,cAAc;YAAE,GAAG,WAAW;YAAE,SAAS;QAAc;IACzD;IAEA,sCAAsC;IACtC,MAAM,WAAW;QACf;QACA;QACA,OAAO;YACL,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,QAAQ,KAAK,UAAU,MAAM,KAAK,GAAG;YACnE,SAAS,MAAM,OAAO;QACxB;QACA;QACA,SAAS;YACP,QAAQ,SAAS;YACjB,IAAI,SAAS;YACb,WAAW,SAAS;YACpB,UAAU,SAAS;YACnB,QAAQ,SAAS;YACjB,WAAW,SAAS;YACpB,WAAW,SAAS;QACtB;QACA,UAAU,YAAY,QAAQ;QAC9B,UAAU,YAAY,QAAQ;IAChC;IAEA,0CAA0C;IAC1C,SAAS;IAET,kCAAkC;IAClC,IAAI,YAAY,QAAQ,KAAK,YAAY;QACvC,uBAAuB;IACzB;IAEA,mBAAmB;IACnB,MAAM,eAAoB;QACxB,OAAO;YACL,MAAM,YAAY,IAAI;YACtB,SAAS,YAAY,OAAO;YAC5B,GAAI,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW;gBAAE;YAAQ,CAAC;QAChE;IACF;IAEA,2CAA2C;IAC3C,IAAI,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,oDAAyB,eAAe;QACzF,aAAa,KAAK,GAAG;YACnB,eAAe,MAAM,OAAO;YAC5B;YACA,UAAU,YAAY,QAAQ;YAC9B,UAAU,YAAY,QAAQ;QAChC;IACF;IAEA,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,cAAc;QAAE,QAAQ,YAAY,UAAU;IAAC;IAElF,uBAAuB;IACvB,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAExC,OAAO;AACT;AAEA,SAAS,mBAAmB,KAAU;IACpC,6BAA6B;IAC7B,IAAI,MAAM,IAAI,IAAI,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;QAC5C,OAAO,cAAc,CAAC,MAAM,IAAI,CAAC;IACnC;IAEA,6BAA6B;IAC7B,IAAI,MAAM,IAAI,IAAI,cAAc,CAAC,MAAM,IAAI,CAAC,EAAE;QAC5C,OAAO,cAAc,CAAC,MAAM,IAAI,CAAC;IACnC;IAEA,0CAA0C;IAC1C,MAAM,UAAU,MAAM,OAAO,EAAE,iBAAiB;IAEhD,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,sBAAsB;QAC3E,OAAO,cAAc,CAAC,aAAa;IACrC;IAEA,IAAI,QAAQ,QAAQ,CAAC,iBAAiB,QAAQ,QAAQ,CAAC,YAAY;QACjE,OAAO,cAAc,CAAC,mBAAmB;IAC3C;IAEA,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,iBAAiB;QAChE,OAAO,cAAc,CAAC,gBAAgB;IACxC;IAEA,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ,QAAQ,CAAC,kBAAkB;QACtE,OAAO,cAAc,CAAC,WAAW;IACnC;IAEA,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,QAAQ,QAAQ,CAAC,mBAAmB;QACvE,OAAO,cAAc,CAAC,QAAQ;IAChC;IAEA,6BAA6B;IAC7B,IAAI,mBAAmB,UAAU;QAC/B,OAAO,cAAc,CAAC,wBAAwB;IAChD;IAEA,IAAI,iBAAiB,UAAU;QAC7B,OAAO,cAAc,CAAC,cAAc;IACtC;IAEA,kBAAkB;IAClB,IAAI,QAAQ,QAAQ,CAAC,eAAe,QAAQ,QAAQ,CAAC,iBAAiB,MAAM,IAAI,EAAE,WAAW,OAAO;QAClG,OAAO,cAAc,CAAC,iBAAiB;IACzC;IAEA,4BAA4B;IAC5B,OAAO,cAAc,CAAC,iBAAiB;AACzC;AAEA,SAAS,mBAAmB,OAAe;IACzC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAClD;AAEA,SAAS,iBAAiB,OAAe;IACvC,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,OAAO,YAAY,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC;AAClD;AAEA,SAAS,SAAS,QAAa;IAC7B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,SAAS,WAAW;IAEnD,wCAAwC;IACxC,OAAQ;QACN,KAAK;YACH,QAAQ,KAAK,CAAC,sBAAsB;YACpC;QACF,KAAK;YACH,QAAQ,KAAK,CAAC,0BAA0B;YACxC;QACF,KAAK;YACH,QAAQ,IAAI,CAAC,6BAA6B;YAC1C;QACF,KAAK;YACH,IAAI,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS;gBAC/C,QAAQ,IAAI,CAAC,0BAA0B;YACzC;YACA;QACF;YACE,QAAQ,KAAK,CAAC,UAAU;IAC5B;IAEA,mDAAmD;IACnD,IAAI,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,aAAa,cAAc,aAAa,MAAM,GAAG;QAC9E,0DAA0D;QAC1D,QAAQ,GAAG,CAAC,yBAAyB,SAAS,OAAO;IACvD;IAEA,yCAAyC;IACzC,IAAI,aAAa,cAAc,aAAa,QAAQ;QAClD,sBAAsB;IACxB;AACF;AAEA,SAAS,uBAAuB,QAAa;IAC3C,0EAA0E;IAC1E,QAAQ,KAAK,CAAC,8BAA8B;QAC1C,SAAS,SAAS,OAAO;QACzB,SAAS,SAAS,WAAW,CAAC,OAAO;QACrC,UAAU,SAAS,WAAW,CAAC,QAAQ;QACvC,WAAW,SAAS,SAAS;QAC7B,SAAS,SAAS,OAAO;IAC3B;IAEA,sCAAsC;IACtC,IAAI,QAAQ,GAAG,CAAC,WAAW,EAAE;QAC3B,0DAA0D;QAC1D,QAAQ,GAAG,CAAC,iCAAiC,SAAS,OAAO;IAC/D;AACF;AAEA,SAAS,sBAAsB,QAAa;IAC1C,4EAA4E;IAC5E,IAAI,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,cAAc,EAAE;QACzC,QAAQ,GAAG,CAAC,uBAAuB;YACjC,MAAM;YACN,SAAS,SAAS,OAAO;YACzB,UAAU,SAAS,WAAW,CAAC,QAAQ;YACvC,UAAU,SAAS,WAAW,CAAC,QAAQ;YACvC,QAAQ,SAAS,OAAO,CAAC,MAAM;YAC/B,IAAI,SAAS,OAAO,CAAC,EAAE;YACvB,UAAU,SAAS,OAAO,CAAC,QAAQ;YACnC,WAAW,SAAS,SAAS;QAC/B;IACF;AACF;AAGO,SAAS,sBAAsB,OAAe,EAAE,OAAa;IAClE,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,IAAI,SAAS;QACV,MAAc,OAAO,GAAG;IAC3B;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,UAAkB,yBAAyB,EAAE,IAAa;IACxF,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG,QAAQ;IACrB,OAAO;AACT;AAEO,SAAS,qBAAqB,UAAkB,eAAe;IACpE,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,OAAO;AACT;AAEO,SAAS,oBAAoB,OAAe,EAAE,IAAmF;IACtI,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,OAAO;AACT;AAEO,SAAS,qBAAqB,UAAmB;IACtD,MAAM,QAAQ,IAAI,MAAM;IACxB,MAAM,IAAI,GAAG;IACb,IAAI,YAAY;QACb,MAAc,UAAU,GAAG;IAC9B;IACA,OAAO;AACT;AAGO,SAAS,kBAAkB,OAAqB,EAAE,MAAe;IACtE,IAAI,CAAC,SAAS;QACZ,OAAO;YACL,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,qGAAA,CAAA,UAAM,CAAC,UAAU;QAC9B;IACF;IAEA,OAAO;QACL;QACA,IAAI,YAAY;QAChB,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;QAChD,UAAU,QAAQ,OAAO,CAAC,QAAQ;QAClC,QAAQ,QAAQ,MAAM;QACtB,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,qGAAA,CAAA,UAAM,CAAC,UAAU;QAC5B,WAAW,QAAQ,OAAO,CAAC,GAAG,CAAC,YAAY;IAC7C;AACF;AAEA,SAAS,YAAY,OAAoB;IACvC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;IACtC,IAAI,WAAW;QACb,OAAO,UAAU,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;IACrC;IAEA,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;IACnC,IAAI,QAAQ;QACV,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC3C,IAAI,gBAAgB;QAClB,OAAO;IACT;IAEA,OAAO;AACT;AAGO,SAAS,kBACd,OAAmC,EACnC,OAA+B;IAE/B,OAAO,OAAO,GAAG;QACf,IAAI;YACF,OAAO,MAAM,WAAW;QAC1B,EAAE,OAAO,OAAO;YACd,MAAM,kBAAkB,OAAO,WAAW;QAC5C;IACF;AACF", "debugId": null}}, {"offset": {"line": 1435, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/utils.ts"], "sourcesContent": ["import { NextResponse, NextRequest } from 'next/server'\nimport { securityConfig } from './config'\nimport crypto from 'crypto'\n\n// Enhanced security headers helper\nexport function addSecurityHeaders(response: NextResponse): NextResponse {\n  const config = securityConfig.headers\n\n  // Always set basic security headers\n  response.headers.set('X-Content-Type-Options', 'nosniff')\n  response.headers.set('X-DNS-Prefetch-Control', 'off')\n  response.headers.set('X-Download-Options', 'noopen')\n\n  if (config.enableXFrameOptions) {\n    response.headers.set('X-Frame-Options', 'DENY')\n  }\n\n  if (config.enableReferrerPolicy) {\n    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')\n  }\n\n  // Enhanced permissions policy\n  response.headers.set('Permissions-Policy',\n    'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'\n  )\n\n  // Content Security Policy\n  if (config.enableCSP) {\n    const cspDirectives = [\n      \"default-src 'self'\",\n      \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com\",\n      \"style-src 'self' 'unsafe-inline' https://fonts.googleapis.com\",\n      \"img-src 'self' data: https: blob:\",\n      \"font-src 'self' data: https://fonts.gstatic.com\",\n      \"connect-src 'self' https://api.openai.com https://maps.googleapis.com\",\n      \"frame-src 'none'\",\n      \"object-src 'none'\",\n      \"base-uri 'self'\",\n      \"form-action 'self'\"\n    ]\n    response.headers.set('Content-Security-Policy', cspDirectives.join('; '))\n  }\n\n  // HSTS for HTTPS\n  if (config.enableHSTS && process.env.NODE_ENV === 'production') {\n    response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')\n  }\n\n  // Remove server information\n  response.headers.delete('Server')\n  response.headers.delete('X-Powered-By')\n  response.headers.delete('X-AspNet-Version')\n  response.headers.delete('X-AspNetMvc-Version')\n\n  // Add cache control for sensitive endpoints\n  if (response.url?.includes('/api/auth/') || response.url?.includes('/api/user/')) {\n    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate')\n    response.headers.set('Pragma', 'no-cache')\n    response.headers.set('Expires', '0')\n  }\n\n  return response\n}\n\n// CSRF Protection\nexport function generateCSRFToken(): string {\n  return crypto.randomBytes(32).toString('hex')\n}\n\nexport function validateCSRFToken(request: NextRequest, sessionToken?: string): boolean {\n  if (!securityConfig.csrf.enabled) {\n    return true\n  }\n\n  const headerToken = request.headers.get(securityConfig.csrf.headerName)\n  const cookieToken = request.cookies.get(securityConfig.csrf.cookieName)?.value\n\n  if (!headerToken || !cookieToken || !sessionToken) {\n    return false\n  }\n\n  // Use timing-safe comparison\n  return crypto.timingSafeEqual(\n    Buffer.from(headerToken, 'hex'),\n    Buffer.from(cookieToken, 'hex')\n  ) && crypto.timingSafeEqual(\n    Buffer.from(cookieToken, 'hex'),\n    Buffer.from(sessionToken, 'hex')\n  )\n}\n\n// Enhanced input sanitization helpers\nexport function sanitizeString(input: string, options: {\n  maxLength?: number\n  allowHTML?: boolean\n  allowSpecialChars?: boolean\n} = {}): string {\n  if (!input || typeof input !== 'string') return ''\n\n  const { maxLength = 10000, allowHTML = false, allowSpecialChars = true } = options\n\n  let sanitized = input.trim()\n\n  if (!allowHTML) {\n    // Enhanced HTML sanitization\n    sanitized = sanitized\n      .replace(/</g, '&lt;')\n      .replace(/>/g, '&gt;')\n      .replace(/\"/g, '&quot;')\n      .replace(/'/g, '&#x27;')\n      .replace(/\\//g, '&#x2F;')\n      .replace(/javascript:/gi, '')\n      .replace(/data:/gi, '')\n      .replace(/vbscript:/gi, '')\n      .replace(/on\\w+\\s*=/gi, '')\n  }\n\n  if (!allowSpecialChars) {\n    // Remove special characters that could be used for injection\n    sanitized = sanitized.replace(/[^\\w\\s\\-_.@]/g, '')\n  }\n\n  // Remove null bytes and control characters\n  sanitized = sanitized.replace(/[\\x00-\\x1f\\x7f-\\x9f]/g, '')\n\n  return sanitized.slice(0, maxLength)\n}\n\nexport function sanitizeNumber(input: any, options: {\n  min?: number\n  max?: number\n  integer?: boolean\n} = {}): number | null {\n  const { min = -Infinity, max = Infinity, integer = false } = options\n\n  let num = Number(input)\n  if (isNaN(num) || !isFinite(num)) return null\n\n  if (integer) {\n    num = Math.floor(num)\n  }\n\n  return Math.max(min, Math.min(max, num))\n}\n\nexport function sanitizeArray(input: any, options: {\n  maxLength?: number\n  itemSanitizer?: (item: any) => any\n} = {}): any[] {\n  const { maxLength = 100, itemSanitizer } = options\n\n  if (!Array.isArray(input)) return []\n\n  let sanitized = input.slice(0, maxLength)\n\n  if (itemSanitizer) {\n    sanitized = sanitized.map(itemSanitizer)\n  }\n\n  return sanitized\n}\n\nexport function sanitizeObject(input: any, allowedKeys: string[], options: {\n  deep?: boolean\n  sanitizeValues?: boolean\n} = {}): any {\n  const { deep = false, sanitizeValues = true } = options\n\n  if (typeof input !== 'object' || input === null || Array.isArray(input)) return {}\n\n  const sanitized: any = {}\n  for (const key of allowedKeys) {\n    if (key in input) {\n      let value = input[key]\n\n      if (sanitizeValues) {\n        if (typeof value === 'string') {\n          value = sanitizeString(value)\n        } else if (typeof value === 'number') {\n          value = sanitizeNumber(value)\n        } else if (Array.isArray(value)) {\n          value = sanitizeArray(value)\n        } else if (deep && typeof value === 'object' && value !== null) {\n          value = sanitizeObject(value, Object.keys(value), options)\n        }\n      }\n\n      sanitized[key] = value\n    }\n  }\n  return sanitized\n}\n\n// Request size validation\nexport function validateRequestSize(request: NextRequest): boolean {\n  const contentLength = request.headers.get('content-length')\n  if (contentLength) {\n    const size = parseInt(contentLength, 10)\n    return size <= securityConfig.validation.maxRequestSize\n  }\n  return true\n}\n\n// IP validation and extraction\nexport function getClientIP(request: NextRequest): string {\n  // Try to get IP from various headers (in order of preference)\n  const headers = [\n    'cf-connecting-ip', // Cloudflare\n    'x-real-ip', // Nginx\n    'x-forwarded-for', // Standard\n    'x-client-ip',\n    'x-cluster-client-ip',\n    'forwarded'\n  ]\n\n  for (const header of headers) {\n    const value = request.headers.get(header)\n    if (value) {\n      // Handle comma-separated IPs (take the first one)\n      const ip = value.split(',')[0].trim()\n      if (isValidIP(ip)) {\n        return ip\n      }\n    }\n  }\n\n  return 'unknown'\n}\n\nfunction isValidIP(ip: string): boolean {\n  // Basic IP validation (IPv4 and IPv6)\n  const ipv4Regex = /^(\\d{1,3}\\.){3}\\d{1,3}$/\n  const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/\n\n  return ipv4Regex.test(ip) || ipv6Regex.test(ip)\n}\n\n// Security event logging\nexport function logSecurityEvent(event: {\n  type: 'auth_failure' | 'rate_limit' | 'validation_error' | 'suspicious_activity'\n  ip: string\n  userAgent?: string\n  details?: any\n}) {\n  if (securityConfig.logging.enableSecurityEvents) {\n    console.warn('Security Event:', {\n      ...event,\n      timestamp: new Date().toISOString(),\n      severity: event.type === 'suspicious_activity' ? 'high' : 'medium'\n    })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AACA;AACA;;;AAGO,SAAS,mBAAmB,QAAsB;IACvD,MAAM,SAAS,kIAAA,CAAA,iBAAc,CAAC,OAAO;IAErC,oCAAoC;IACpC,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,0BAA0B;IAC/C,SAAS,OAAO,CAAC,GAAG,CAAC,sBAAsB;IAE3C,IAAI,OAAO,mBAAmB,EAAE;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC1C;IAEA,IAAI,OAAO,oBAAoB,EAAE;QAC/B,SAAS,OAAO,CAAC,GAAG,CAAC,mBAAmB;IAC1C;IAEA,8BAA8B;IAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,sBACnB;IAGF,0BAA0B;IAC1B,IAAI,OAAO,SAAS,EAAE;QACpB,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS,OAAO,CAAC,GAAG,CAAC,2BAA2B,cAAc,IAAI,CAAC;IACrE;IAEA,iBAAiB;IACjB,uCAAgE;;IAEhE;IAEA,4BAA4B;IAC5B,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;IACxB,SAAS,OAAO,CAAC,MAAM,CAAC;IAExB,4CAA4C;IAC5C,IAAI,SAAS,GAAG,EAAE,SAAS,iBAAiB,SAAS,GAAG,EAAE,SAAS,eAAe;QAChF,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB;QACtC,SAAS,OAAO,CAAC,GAAG,CAAC,UAAU;QAC/B,SAAS,OAAO,CAAC,GAAG,CAAC,WAAW;IAClC;IAEA,OAAO;AACT;AAGO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;AACzC;AAEO,SAAS,kBAAkB,OAAoB,EAAE,YAAqB;IAC3E,IAAI,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,OAAO,EAAE;QAChC,OAAO;IACT;IAEA,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,UAAU;IACtE,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,UAAU,GAAG;IAEzE,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,cAAc;QACjD,OAAO;IACT;IAEA,6BAA6B;IAC7B,OAAO,qGAAA,CAAA,UAAM,CAAC,eAAe,CAC3B,OAAO,IAAI,CAAC,aAAa,QACzB,OAAO,IAAI,CAAC,aAAa,WACtB,qGAAA,CAAA,UAAM,CAAC,eAAe,CACzB,OAAO,IAAI,CAAC,aAAa,QACzB,OAAO,IAAI,CAAC,cAAc;AAE9B;AAGO,SAAS,eAAe,KAAa,EAAE,UAI1C,CAAC,CAAC;IACJ,IAAI,CAAC,SAAS,OAAO,UAAU,UAAU,OAAO;IAEhD,MAAM,EAAE,YAAY,KAAK,EAAE,YAAY,KAAK,EAAE,oBAAoB,IAAI,EAAE,GAAG;IAE3E,IAAI,YAAY,MAAM,IAAI;IAE1B,IAAI,CAAC,WAAW;QACd,6BAA6B;QAC7B,YAAY,UACT,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,QACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,MAAM,UACd,OAAO,CAAC,OAAO,UACf,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,WAAW,IACnB,OAAO,CAAC,eAAe,IACvB,OAAO,CAAC,eAAe;IAC5B;IAEA,IAAI,CAAC,mBAAmB;QACtB,6DAA6D;QAC7D,YAAY,UAAU,OAAO,CAAC,iBAAiB;IACjD;IAEA,2CAA2C;IAC3C,YAAY,UAAU,OAAO,CAAC,yBAAyB;IAEvD,OAAO,UAAU,KAAK,CAAC,GAAG;AAC5B;AAEO,SAAS,eAAe,KAAU,EAAE,UAIvC,CAAC,CAAC;IACJ,MAAM,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG;IAE7D,IAAI,MAAM,OAAO;IACjB,IAAI,MAAM,QAAQ,CAAC,SAAS,MAAM,OAAO;IAEzC,IAAI,SAAS;QACX,MAAM,KAAK,KAAK,CAAC;IACnB;IAEA,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK;AACrC;AAEO,SAAS,cAAc,KAAU,EAAE,UAGtC,CAAC,CAAC;IACJ,MAAM,EAAE,YAAY,GAAG,EAAE,aAAa,EAAE,GAAG;IAE3C,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ,OAAO,EAAE;IAEpC,IAAI,YAAY,MAAM,KAAK,CAAC,GAAG;IAE/B,IAAI,eAAe;QACjB,YAAY,UAAU,GAAG,CAAC;IAC5B;IAEA,OAAO;AACT;AAEO,SAAS,eAAe,KAAU,EAAE,WAAqB,EAAE,UAG9D,CAAC,CAAC;IACJ,MAAM,EAAE,OAAO,KAAK,EAAE,iBAAiB,IAAI,EAAE,GAAG;IAEhD,IAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,MAAM,OAAO,CAAC,QAAQ,OAAO,CAAC;IAEjF,MAAM,YAAiB,CAAC;IACxB,KAAK,MAAM,OAAO,YAAa;QAC7B,IAAI,OAAO,OAAO;YAChB,IAAI,QAAQ,KAAK,CAAC,IAAI;YAEtB,IAAI,gBAAgB;gBAClB,IAAI,OAAO,UAAU,UAAU;oBAC7B,QAAQ,eAAe;gBACzB,OAAO,IAAI,OAAO,UAAU,UAAU;oBACpC,QAAQ,eAAe;gBACzB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;oBAC/B,QAAQ,cAAc;gBACxB,OAAO,IAAI,QAAQ,OAAO,UAAU,YAAY,UAAU,MAAM;oBAC9D,QAAQ,eAAe,OAAO,OAAO,IAAI,CAAC,QAAQ;gBACpD;YACF;YAEA,SAAS,CAAC,IAAI,GAAG;QACnB;IACF;IACA,OAAO;AACT;AAGO,SAAS,oBAAoB,OAAoB;IACtD,MAAM,gBAAgB,QAAQ,OAAO,CAAC,GAAG,CAAC;IAC1C,IAAI,eAAe;QACjB,MAAM,OAAO,SAAS,eAAe;QACrC,OAAO,QAAQ,kIAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,cAAc;IACzD;IACA,OAAO;AACT;AAGO,SAAS,YAAY,OAAoB;IAC9C,8DAA8D;IAC9D,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;KACD;IAED,KAAK,MAAM,UAAU,QAAS;QAC5B,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC;QAClC,IAAI,OAAO;YACT,kDAAkD;YAClD,MAAM,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;YACnC,IAAI,UAAU,KAAK;gBACjB,OAAO;YACT;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,UAAU,EAAU;IAC3B,sCAAsC;IACtC,MAAM,YAAY;IAClB,MAAM,YAAY;IAElB,OAAO,UAAU,IAAI,CAAC,OAAO,UAAU,IAAI,CAAC;AAC9C;AAGO,SAAS,iBAAiB,KAKhC;IACC,IAAI,kIAAA,CAAA,iBAAc,CAAC,OAAO,CAAC,oBAAoB,EAAE;QAC/C,QAAQ,IAAI,CAAC,mBAAmB;YAC9B,GAAG,KAAK;YACR,WAAW,IAAI,OAAO,WAAW;YACjC,UAAU,MAAM,IAAI,KAAK,wBAAwB,SAAS;QAC5D;IACF;AACF", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/security/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createClient } from '@/lib/supabase/server'\nimport { rateLimit } from './rate-limit'\nimport { validateInput } from './validation'\nimport { handleSecureError } from './error-handler'\nimport { securityConfig } from './config'\nimport {\n  validateCSRFToken,\n  validateRequestSize,\n  getClientIP,\n  logSecurityEvent,\n  addSecurityHeaders\n} from './utils'\n\nexport interface SecurityOptions {\n  requireAuth?: boolean\n  rateLimit?: {\n    requests: number\n    window: number\n  }\n  validation?: {\n    body?: any\n    query?: any\n  }\n  auditLog?: boolean\n  requireEmailVerification?: boolean\n  allowedRoles?: string[]\n  csrfProtection?: boolean\n}\n\nexport function withSecurity(\n  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,\n  options: SecurityOptions = {}\n) {\n  return async (request: NextRequest, context?: any) => {\n    try {\n      const supabase = await createClient()\n      \n      // Enhanced authentication check\n      if (options.requireAuth !== false) {\n        const authResult = await validateAuthentication(request, supabase, options)\n        if (!authResult.success) {\n          return NextResponse.json({\n            error: {\n              code: authResult.errorCode,\n              message: authResult.message\n            }\n          }, { status: authResult.status })\n        }\n\n        // Add user to context\n        if (context) {\n          context.user = authResult.user\n        }\n      }\n\n      // Enhanced security checks\n      const securityChecks = await performSecurityChecks(request, options)\n      if (!securityChecks.success) {\n        return NextResponse.json(\n          {\n            error: {\n              code: securityChecks.errorCode,\n              message: securityChecks.message,\n              retryAfter: securityChecks.retryAfter\n            }\n          },\n          {\n            status: securityChecks.status,\n            headers: securityChecks.headers || {}\n          }\n        )\n      }\n\n      // Add security check results to context\n      if (context) {\n        context.securityInfo = securityChecks.info\n      }\n\n      // Input validation\n      if (options.validation) {\n        const validationResult = await validateInput(request, options.validation)\n        if (!validationResult.success) {\n          return NextResponse.json(\n            {\n              error: {\n                code: 'VALIDATION_ERROR',\n                message: 'Invalid input data',\n                details: validationResult.errors\n              }\n            },\n            { status: 400 }\n          )\n        }\n\n        // Add validated data to context to avoid re-reading body\n        if (context && validationResult.data) {\n          context.validatedData = validationResult.data\n        }\n      }\n\n      // Execute the handler\n      const response = await handler(request, context)\n\n      // Add security headers to response\n      const secureResponse = addSecurityHeaders(response)\n\n      // Audit logging\n      if (options.auditLog && context?.user) {\n        await logAuditEvent(request, context.user, secureResponse)\n      }\n\n      return secureResponse\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  }\n}\n\nasync function performSecurityChecks(\n  request: NextRequest,\n  options: SecurityOptions\n): Promise<{\n  success: boolean\n  errorCode?: string\n  message?: string\n  status?: number\n  retryAfter?: number\n  headers?: Record<string, string>\n  info?: any\n}> {\n  const clientIP = getClientIP(request)\n  const userAgent = request.headers.get('user-agent') || ''\n\n  // 1. Request size validation\n  if (!validateRequestSize(request)) {\n    logSecurityEvent({\n      type: 'validation_error',\n      ip: clientIP,\n      userAgent,\n      details: { reason: 'request_too_large' }\n    })\n\n    return {\n      success: false,\n      errorCode: 'REQUEST_TOO_LARGE',\n      message: 'Request size exceeds limit',\n      status: 413\n    }\n  }\n\n  // 2. Suspicious user agent detection\n  const suspiciousPatterns = [\n    /bot/i, /crawler/i, /spider/i, /scraper/i, /scanner/i,\n    /curl/i, /wget/i, /python/i, /java/i, /perl/i\n  ]\n\n  if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {\n    logSecurityEvent({\n      type: 'suspicious_activity',\n      ip: clientIP,\n      userAgent,\n      details: { reason: 'suspicious_user_agent' }\n    })\n\n    return {\n      success: false,\n      errorCode: 'SUSPICIOUS_REQUEST',\n      message: 'Request blocked',\n      status: 403\n    }\n  }\n\n  // 3. Rate limiting\n  if (options.rateLimit && securityConfig.rateLimit.enabled) {\n    const rateLimitResult = await rateLimit(\n      `${clientIP}:${request.nextUrl.pathname}`,\n      options.rateLimit.requests,\n      options.rateLimit.window\n    )\n\n    if (!rateLimitResult.success) {\n      logSecurityEvent({\n        type: 'rate_limit',\n        ip: clientIP,\n        userAgent,\n        details: { endpoint: request.nextUrl.pathname }\n      })\n\n      return {\n        success: false,\n        errorCode: 'RATE_LIMIT',\n        message: 'Too many requests',\n        status: 429,\n        retryAfter: rateLimitResult.retryAfter,\n        headers: {\n          'Retry-After': rateLimitResult.retryAfter?.toString() || '60'\n        }\n      }\n    }\n  }\n\n  // 4. CORS validation\n  if (securityConfig.cors.enabled) {\n    const origin = request.headers.get('origin')\n    const method = request.method\n\n    if (origin && !securityConfig.cors.allowedOrigins.includes(origin) && !securityConfig.cors.allowedOrigins.includes('*')) {\n      logSecurityEvent({\n        type: 'suspicious_activity',\n        ip: clientIP,\n        userAgent,\n        details: { reason: 'invalid_origin', origin }\n      })\n\n      return {\n        success: false,\n        errorCode: 'CORS_ERROR',\n        message: 'Origin not allowed',\n        status: 403\n      }\n    }\n\n    if (!securityConfig.cors.allowedMethods.includes(method)) {\n      return {\n        success: false,\n        errorCode: 'METHOD_NOT_ALLOWED',\n        message: 'Method not allowed',\n        status: 405\n      }\n    }\n  }\n\n  // 5. CSRF protection for state-changing operations\n  if (securityConfig.csrf.enabled && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(request.method)) {\n    if (!validateCSRFToken(request)) {\n      logSecurityEvent({\n        type: 'suspicious_activity',\n        ip: clientIP,\n        userAgent,\n        details: { reason: 'csrf_token_invalid' }\n      })\n\n      return {\n        success: false,\n        errorCode: 'CSRF_ERROR',\n        message: 'CSRF token validation failed',\n        status: 403\n      }\n    }\n  }\n\n  // 6. Content-Type validation for POST/PUT requests\n  if (['POST', 'PUT', 'PATCH'].includes(request.method)) {\n    const contentType = request.headers.get('content-type')\n    if (contentType && !contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {\n      return {\n        success: false,\n        errorCode: 'INVALID_CONTENT_TYPE',\n        message: 'Invalid content type',\n        status: 415\n      }\n    }\n  }\n\n  return {\n    success: true,\n    info: {\n      clientIP,\n      userAgent,\n      timestamp: new Date().toISOString()\n    }\n  }\n}\n\nasync function validateAuthentication(\n  request: NextRequest,\n  supabase: any,\n  options: SecurityOptions\n) {\n  try {\n    // Get user from JWT token\n    const { data: { user }, error } = await supabase.auth.getUser()\n\n    if (error || !user) {\n      return {\n        success: false,\n        errorCode: 'AUTH_REQUIRED',\n        message: 'Authentication required',\n        status: 401\n      }\n    }\n\n    // Check if email verification is required\n    if (options.requireEmailVerification && !user.email_confirmed_at) {\n      return {\n        success: false,\n        errorCode: 'EMAIL_NOT_VERIFIED',\n        message: 'Email verification required',\n        status: 403\n      }\n    }\n\n    // Check user roles if specified\n    if (options.allowedRoles && options.allowedRoles.length > 0) {\n      const userRole = user.user_metadata?.role || 'user'\n      if (!options.allowedRoles.includes(userRole)) {\n        return {\n          success: false,\n          errorCode: 'INSUFFICIENT_PERMISSIONS',\n          message: 'Insufficient permissions',\n          status: 403\n        }\n      }\n    }\n\n    // Validate session freshness (check if token is not too old)\n    const { data: { session } } = await supabase.auth.getSession()\n    if (session) {\n      const tokenAge = Date.now() - (session.expires_at ? session.expires_at * 1000 : 0)\n      const maxAge = 24 * 60 * 60 * 1000 // 24 hours\n\n      if (tokenAge > maxAge) {\n        return {\n          success: false,\n          errorCode: 'SESSION_EXPIRED',\n          message: 'Session expired, please login again',\n          status: 401\n        }\n      }\n    }\n\n    return {\n      success: true,\n      user\n    }\n  } catch (error) {\n    console.error('Authentication validation error:', error)\n    return {\n      success: false,\n      errorCode: 'AUTH_ERROR',\n      message: 'Authentication error',\n      status: 500\n    }\n  }\n}\n\nasync function logAuditEvent(\n  request: NextRequest,\n  user: any,\n  response: NextResponse\n) {\n  try {\n    const supabase = await createClient()\n\n    const action = `${request.method} ${request.nextUrl.pathname}`\n    const clientIP = request.ip ||\n      request.headers.get('x-forwarded-for') ||\n      request.headers.get('x-real-ip')\n\n    await supabase\n      .from('audit_logs')\n      .insert([\n        {\n          user_id: user.id,\n          action,\n          ip_address: clientIP,\n          user_agent: request.headers.get('user-agent'),\n          new_values: {\n            status: response.status,\n            method: request.method,\n            path: request.nextUrl.pathname,\n            timestamp: new Date().toISOString()\n          }\n        }\n      ])\n  } catch (error) {\n    console.error('Failed to log audit event:', error)\n  }\n}\n\n\n\n\n\n// Common validation schemas\nexport const commonSchemas = {\n  task: {\n    title: { required: true, type: 'string', maxLength: 200 },\n    description: { required: false, type: 'string', maxLength: 1000 },\n    priority: { required: false, type: 'string', enum: ['low', 'medium', 'high'] },\n    due_date: { required: false, type: 'string' },\n    category: { required: false, type: 'string', maxLength: 100 },\n    estimated_duration: { required: false, type: 'number', min: 1, max: 1440 }\n  },\n  recipe: {\n    title: { required: true, type: 'string', maxLength: 200 },\n    description: { required: false, type: 'string', maxLength: 2000 },\n    ingredients: { required: true, type: 'array', maxItems: 50 },\n    instructions: { required: true, type: 'array', maxItems: 50 },\n    prep_time: { required: false, type: 'number', min: 0, max: 1440 },\n    cook_time: { required: false, type: 'number', min: 0, max: 1440 },\n    servings: { required: false, type: 'number', min: 1, max: 50 },\n    difficulty: { required: false, type: 'string', enum: ['easy', 'medium', 'hard'] }\n  },\n  shoppingListItem: {\n    name: { required: true, type: 'string', maxLength: 200 },\n    quantity: { required: false, type: 'number', min: 0 },\n    unit: { required: false, type: 'string', maxLength: 50 },\n    category: { required: false, type: 'string', maxLength: 100 },\n    priority: { required: false, type: 'number', min: 1, max: 5 }\n  },\n  transaction: {\n    amount: { required: true, type: 'number' },\n    description: { required: true, type: 'string', maxLength: 200 },\n    category_id: { required: true, type: 'string' },\n    transaction_type: { required: true, type: 'string', enum: ['income', 'expense'] },\n    date: { required: true, type: 'string' }\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAwBO,SAAS,aACd,OAAuE,EACvE,UAA2B,CAAC,CAAC;IAE7B,OAAO,OAAO,SAAsB;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;YAElC,gCAAgC;YAChC,IAAI,QAAQ,WAAW,KAAK,OAAO;gBACjC,MAAM,aAAa,MAAM,uBAAuB,SAAS,UAAU;gBACnE,IAAI,CAAC,WAAW,OAAO,EAAE;oBACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;wBACvB,OAAO;4BACL,MAAM,WAAW,SAAS;4BAC1B,SAAS,WAAW,OAAO;wBAC7B;oBACF,GAAG;wBAAE,QAAQ,WAAW,MAAM;oBAAC;gBACjC;gBAEA,sBAAsB;gBACtB,IAAI,SAAS;oBACX,QAAQ,IAAI,GAAG,WAAW,IAAI;gBAChC;YACF;YAEA,2BAA2B;YAC3B,MAAM,iBAAiB,MAAM,sBAAsB,SAAS;YAC5D,IAAI,CAAC,eAAe,OAAO,EAAE;gBAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBACE,OAAO;wBACL,MAAM,eAAe,SAAS;wBAC9B,SAAS,eAAe,OAAO;wBAC/B,YAAY,eAAe,UAAU;oBACvC;gBACF,GACA;oBACE,QAAQ,eAAe,MAAM;oBAC7B,SAAS,eAAe,OAAO,IAAI,CAAC;gBACtC;YAEJ;YAEA,wCAAwC;YACxC,IAAI,SAAS;gBACX,QAAQ,YAAY,GAAG,eAAe,IAAI;YAC5C;YAEA,mBAAmB;YACnB,IAAI,QAAQ,UAAU,EAAE;gBACtB,MAAM,mBAAmB,MAAM,CAAA,GAAA,sIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,QAAQ,UAAU;gBACxE,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;wBACE,OAAO;4BACL,MAAM;4BACN,SAAS;4BACT,SAAS,iBAAiB,MAAM;wBAClC;oBACF,GACA;wBAAE,QAAQ;oBAAI;gBAElB;gBAEA,yDAAyD;gBACzD,IAAI,WAAW,iBAAiB,IAAI,EAAE;oBACpC,QAAQ,aAAa,GAAG,iBAAiB,IAAI;gBAC/C;YACF;YAEA,sBAAsB;YACtB,MAAM,WAAW,MAAM,QAAQ,SAAS;YAExC,mCAAmC;YACnC,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE1C,gBAAgB;YAChB,IAAI,QAAQ,QAAQ,IAAI,SAAS,MAAM;gBACrC,MAAM,cAAc,SAAS,QAAQ,IAAI,EAAE;YAC7C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;QAC3B;IACF;AACF;AAEA,eAAe,sBACb,OAAoB,EACpB,OAAwB;IAUxB,MAAM,WAAW,CAAA,GAAA,iIAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,iBAAiB;IAEvD,6BAA6B;IAC7B,IAAI,CAAC,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU;QACjC,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;YACf,MAAM;YACN,IAAI;YACJ;YACA,SAAS;gBAAE,QAAQ;YAAoB;QACzC;QAEA,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;QACV;IACF;IAEA,qCAAqC;IACrC,MAAM,qBAAqB;QACzB;QAAQ;QAAY;QAAW;QAAY;QAC3C;QAAS;QAAS;QAAW;QAAS;KACvC;IAED,IAAI,mBAAmB,IAAI,CAAC,CAAA,UAAW,QAAQ,IAAI,CAAC,aAAa;QAC/D,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;YACf,MAAM;YACN,IAAI;YACJ;YACA,SAAS;gBAAE,QAAQ;YAAwB;QAC7C;QAEA,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;QACV;IACF;IAEA,mBAAmB;IACnB,IAAI,QAAQ,SAAS,IAAI,kIAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,OAAO,EAAE;QACzD,MAAM,kBAAkB,MAAM,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD,EACpC,GAAG,SAAS,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE,EACzC,QAAQ,SAAS,CAAC,QAAQ,EAC1B,QAAQ,SAAS,CAAC,MAAM;QAG1B,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC5B,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,MAAM;gBACN,IAAI;gBACJ;gBACA,SAAS;oBAAE,UAAU,QAAQ,OAAO,CAAC,QAAQ;gBAAC;YAChD;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;gBACR,YAAY,gBAAgB,UAAU;gBACtC,SAAS;oBACP,eAAe,gBAAgB,UAAU,EAAE,cAAc;gBAC3D;YACF;QACF;IACF;IAEA,qBAAqB;IACrB,IAAI,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,OAAO,EAAE;QAC/B,MAAM,SAAS,QAAQ,OAAO,CAAC,GAAG,CAAC;QACnC,MAAM,SAAS,QAAQ,MAAM;QAE7B,IAAI,UAAU,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM;YACvH,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,MAAM;gBACN,IAAI;gBACJ;gBACA,SAAS;oBAAE,QAAQ;oBAAkB;gBAAO;YAC9C;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;QAEA,IAAI,CAAC,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS;YACxD,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;IACF;IAEA,mDAAmD;IACnD,IAAI,kIAAA,CAAA,iBAAc,CAAC,IAAI,CAAC,OAAO,IAAI;QAAC;QAAQ;QAAO;QAAS;KAAS,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG;QAC9F,IAAI,CAAC,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;YAC/B,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;gBACf,MAAM;gBACN,IAAI;gBACJ;gBACA,SAAS;oBAAE,QAAQ;gBAAqB;YAC1C;YAEA,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;IACF;IAEA,mDAAmD;IACnD,IAAI;QAAC;QAAQ;QAAO;KAAQ,CAAC,QAAQ,CAAC,QAAQ,MAAM,GAAG;QACrD,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,eAAe,CAAC,YAAY,QAAQ,CAAC,uBAAuB,CAAC,YAAY,QAAQ,CAAC,wBAAwB;YAC5G,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;IACF;IAEA,OAAO;QACL,SAAS;QACT,MAAM;YACJ;YACA;YACA,WAAW,IAAI,OAAO,WAAW;QACnC;IACF;AACF;AAEA,eAAe,uBACb,OAAoB,EACpB,QAAa,EACb,OAAwB;IAExB,IAAI;QACF,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAE7D,IAAI,SAAS,CAAC,MAAM;YAClB,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;QAEA,0CAA0C;QAC1C,IAAI,QAAQ,wBAAwB,IAAI,CAAC,KAAK,kBAAkB,EAAE;YAChE,OAAO;gBACL,SAAS;gBACT,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;QACF;QAEA,gCAAgC;QAChC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;YAC3D,MAAM,WAAW,KAAK,aAAa,EAAE,QAAQ;YAC7C,IAAI,CAAC,QAAQ,YAAY,CAAC,QAAQ,CAAC,WAAW;gBAC5C,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,QAAQ;gBACV;YACF;QACF;QAEA,6DAA6D;QAC7D,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAC5D,IAAI,SAAS;YACX,MAAM,WAAW,KAAK,GAAG,KAAK,CAAC,QAAQ,UAAU,GAAG,QAAQ,UAAU,GAAG,OAAO,CAAC;YACjF,MAAM,SAAS,KAAK,KAAK,KAAK,KAAK,WAAW;;YAE9C,IAAI,WAAW,QAAQ;gBACrB,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,QAAQ;gBACV;YACF;QACF;QAEA,OAAO;YACL,SAAS;YACT;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YACL,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;QACV;IACF;AACF;AAEA,eAAe,cACb,OAAoB,EACpB,IAAS,EACT,QAAsB;IAEtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAElC,MAAM,SAAS,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,QAAQ,EAAE;QAC9D,MAAM,WAAW,QAAQ,EAAE,IACzB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBACpB,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEtB,MAAM,SACH,IAAI,CAAC,cACL,MAAM,CAAC;YACN;gBACE,SAAS,KAAK,EAAE;gBAChB;gBACA,YAAY;gBACZ,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC;gBAChC,YAAY;oBACV,QAAQ,SAAS,MAAM;oBACvB,QAAQ,QAAQ,MAAM;oBACtB,MAAM,QAAQ,OAAO,CAAC,QAAQ;oBAC9B,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;SACD;IACL,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;IAC9C;AACF;AAOO,MAAM,gBAAgB;IAC3B,MAAM;QACJ,OAAO;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACxD,aAAa;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAK;QAChE,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,MAAM;gBAAC;gBAAO;gBAAU;aAAO;QAAC;QAC7E,UAAU;YAAE,UAAU;YAAO,MAAM;QAAS;QAC5C,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAI;QAC5D,oBAAoB;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;IAC3E;IACA,QAAQ;QACN,OAAO;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACxD,aAAa;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAK;QAChE,aAAa;YAAE,UAAU;YAAM,MAAM;YAAS,UAAU;QAAG;QAC3D,cAAc;YAAE,UAAU;YAAM,MAAM;YAAS,UAAU;QAAG;QAC5D,WAAW;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;QAChE,WAAW;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAK;QAChE,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAG;QAC7D,YAAY;YAAE,UAAU;YAAO,MAAM;YAAU,MAAM;gBAAC;gBAAQ;gBAAU;aAAO;QAAC;IAClF;IACA,kBAAkB;QAChB,MAAM;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QACvD,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;QAAE;QACpD,MAAM;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAG;QACvD,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,WAAW;QAAI;QAC5D,UAAU;YAAE,UAAU;YAAO,MAAM;YAAU,KAAK;YAAG,KAAK;QAAE;IAC9D;IACA,aAAa;QACX,QAAQ;YAAE,UAAU;YAAM,MAAM;QAAS;QACzC,aAAa;YAAE,UAAU;YAAM,MAAM;YAAU,WAAW;QAAI;QAC9D,aAAa;YAAE,UAAU;YAAM,MAAM;QAAS;QAC9C,kBAAkB;YAAE,UAAU;YAAM,MAAM;YAAU,MAAM;gBAAC;gBAAU;aAAU;QAAC;QAChF,MAAM;YAAE,UAAU;YAAM,MAAM;QAAS;IACzC;AACF", "debugId": null}}, {"offset": {"line": 2097, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/lib/auth/profile-manager.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\n\nexport interface UserProfile {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at?: string\n  updated_at?: string\n}\n\nexport async function ensureUserProfile(userId: string, email: string, fullName?: string): Promise<UserProfile> {\n  const supabase = await createClient()\n\n  try {\n    // First, check if profile already exists\n    const { data: existingProfile, error: fetchError } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n\n    if (existingProfile && !fetchError) {\n      return existingProfile\n    }\n\n    // If profile doesn't exist, create it\n    const { data: newProfile, error: insertError } = await supabase\n      .from('profiles')\n      .insert([\n        {\n          id: userId,\n          email: email,\n          full_name: fullName || email.split('@')[0],\n          created_at: new Date().toISOString(),\n          updated_at: new Date().toISOString()\n        }\n      ])\n      .select()\n      .single()\n\n    if (insertError) {\n      console.error('Error creating user profile:', insertError)\n      throw new Error('Failed to create user profile')\n    }\n\n    return newProfile\n  } catch (error) {\n    console.error('Error in ensureUserProfile:', error)\n    throw error\n  }\n}\n\nexport async function getUserProfile(userId: string): Promise<UserProfile | null> {\n  const supabase = await createClient()\n\n  try {\n    const { data: profile, error } = await supabase\n      .from('profiles')\n      .select('*')\n      .eq('id', userId)\n      .single()\n\n    if (error) {\n      console.error('Error fetching user profile:', error)\n      return null\n    }\n\n    return profile\n  } catch (error) {\n    console.error('Error in getUserProfile:', error)\n    return null\n  }\n}\n\nexport async function updateUserProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile | null> {\n  const supabase = await createClient()\n\n  try {\n    const { data: updatedProfile, error } = await supabase\n      .from('profiles')\n      .update({\n        ...updates,\n        updated_at: new Date().toISOString()\n      })\n      .eq('id', userId)\n      .select()\n      .single()\n\n    if (error) {\n      console.error('Error updating user profile:', error)\n      throw new Error('Failed to update user profile')\n    }\n\n    return updatedProfile\n  } catch (error) {\n    console.error('Error in updateUserProfile:', error)\n    throw error\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAWO,eAAe,kBAAkB,MAAc,EAAE,KAAa,EAAE,QAAiB;IACtF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,yCAAyC;QACzC,MAAM,EAAE,MAAM,eAAe,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,mBAAmB,CAAC,YAAY;YAClC,OAAO;QACT;QAEA,sCAAsC;QACtC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC;YACN;gBACE,IAAI;gBACJ,OAAO;gBACP,WAAW,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC1C,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,aAAa;YACf,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAEO,eAAe,eAAe,MAAc;IACjD,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;IACT;AACF;AAEO,eAAe,kBAAkB,MAAc,EAAE,OAA6B;IACnF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;IAElC,IAAI;QACF,MAAM,EAAE,MAAM,cAAc,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3C,IAAI,CAAC,YACL,MAAM,CAAC;YACN,GAAG,OAAO;YACV,YAAY,IAAI,OAAO,WAAW;QACpC,GACC,EAAE,CAAC,MAAM,QACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/QuantAxiom/Products/LifeManager/src/app/api/recipes/route.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/server'\nimport { NextRequest, NextResponse } from 'next/server'\nimport { withSecurity } from '@/lib/security/middleware'\nimport { addSecurityHeaders } from '@/lib/security/utils'\nimport { rateLimitConfigs } from '@/lib/security/rate-limit'\nimport { handleSecureError } from '@/lib/security/error-handler'\nimport { ensureUserProfile } from '@/lib/auth/profile-manager'\n\n// GET /api/recipes - Get user's recipes\nexport const GET = withSecurity(\n  async (request: NextRequest, context: any) => {\n    try {\n      const supabase = await createClient()\n      const user = context.user\n\n      // Ensure user profile exists\n      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)\n\n      const { data: recipes, error } = await supabase\n        .from('recipes')\n        .select(`\n          id,\n          title,\n          description,\n          prep_time,\n          cook_time,\n          servings,\n          difficulty,\n          cuisine,\n          tags,\n          ingredients,\n          instructions,\n          image_url,\n          source_url,\n          nutritional_info,\n          created_at,\n          updated_at\n        `)\n        .eq('user_id', user.id)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      const response = NextResponse.json({ recipes: recipes || [] })\n      return addSecurityHeaders(response)\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  },\n  {\n    requireAuth: true,\n    rateLimit: rateLimitConfigs.api,\n    auditLog: true\n  }\n)\n\n// POST /api/recipes - Create new recipe\nexport const POST = withSecurity(\n  async (request: NextRequest, context: any) => {\n    try {\n      const requestData = await request.json()\n      const {\n        title,\n        description,\n        prep_time,\n        cook_time,\n        servings,\n        difficulty,\n        cuisine,\n        tags,\n        ingredients,\n        instructions,\n        image_url,\n        source_url\n      } = requestData\n      \n      // Basic validation\n      if (!title || typeof title !== 'string' || title.length > 200) {\n        return NextResponse.json({ \n          error: { code: 'VALIDATION_ERROR', message: 'Invalid title' } \n        }, { status: 400 })\n      }\n\n      const supabase = await createClient()\n      const user = context.user\n\n      // Ensure user profile exists\n      await ensureUserProfile(user.id, user.email, user.user_metadata?.full_name)\n\n      const { data: recipe, error } = await supabase\n        .from('recipes')\n        .insert([\n          {\n            user_id: user.id,\n            title: title.trim(),\n            description: description || null,\n            prep_time: prep_time || null,\n            cook_time: cook_time || null,\n            servings: servings || null,\n            difficulty: difficulty || null,\n            cuisine: cuisine || null,\n            tags: tags || [],\n            ingredients: ingredients || [],\n            instructions: instructions || [],\n            image_url: image_url || null,\n            source_url: source_url || null\n          },\n        ])\n        .select()\n        .single()\n\n      if (error) throw error\n\n      const response = NextResponse.json({ recipe })\n      return addSecurityHeaders(response)\n    } catch (error) {\n      return handleSecureError(error)\n    }\n  },\n  {\n    requireAuth: true,\n    rateLimit: rateLimitConfigs.api,\n    auditLog: true\n  }\n)\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAGO,MAAM,MAAM,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAC5B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,OAAO,QAAQ,IAAI;QAEzB,6BAA6B;QAC7B,MAAM,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE;QAEjE,MAAM,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,WACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;QAiBT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QAEjB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS,WAAW,EAAE;QAAC;QAC5D,OAAO,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B;AACF,GACA;IACE,aAAa;IACb,WAAW,yIAAA,CAAA,mBAAgB,CAAC,GAAG;IAC/B,UAAU;AACZ;AAIK,MAAM,OAAO,CAAA,GAAA,sIAAA,CAAA,eAAY,AAAD,EAC7B,OAAO,SAAsB;IAC3B,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QACtC,MAAM,EACJ,KAAK,EACL,WAAW,EACX,SAAS,EACT,SAAS,EACT,QAAQ,EACR,UAAU,EACV,OAAO,EACP,IAAI,EACJ,WAAW,EACX,YAAY,EACZ,SAAS,EACT,UAAU,EACX,GAAG;QAEJ,mBAAmB;QACnB,IAAI,CAAC,SAAS,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,KAAK;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;oBAAE,MAAM;oBAAoB,SAAS;gBAAgB;YAC9D,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,kIAAA,CAAA,eAAY,AAAD;QAClC,MAAM,OAAO,QAAQ,IAAI;QAEzB,6BAA6B;QAC7B,MAAM,CAAA,GAAA,0IAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,EAAE,EAAE,KAAK,KAAK,EAAE,KAAK,aAAa,EAAE;QAEjE,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,CAAC;YACN;gBACE,SAAS,KAAK,EAAE;gBAChB,OAAO,MAAM,IAAI;gBACjB,aAAa,eAAe;gBAC5B,WAAW,aAAa;gBACxB,WAAW,aAAa;gBACxB,UAAU,YAAY;gBACtB,YAAY,cAAc;gBAC1B,SAAS,WAAW;gBACpB,MAAM,QAAQ,EAAE;gBAChB,aAAa,eAAe,EAAE;gBAC9B,cAAc,gBAAgB,EAAE;gBAChC,WAAW,aAAa;gBACxB,YAAY,cAAc;YAC5B;SACD,EACA,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QAEjB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAO;QAC5C,OAAO,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE;IAC5B,EAAE,OAAO,OAAO;QACd,OAAO,CAAA,GAAA,4IAAA,CAAA,oBAAiB,AAAD,EAAE;IAC3B;AACF,GACA;IACE,aAAa;IACb,WAAW,yIAAA,CAAA,mBAAgB,CAAC,GAAG;IAC/B,UAAU;AACZ", "debugId": null}}]}