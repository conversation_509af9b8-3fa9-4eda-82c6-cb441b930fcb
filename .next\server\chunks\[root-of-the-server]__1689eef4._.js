module.exports = {

"[project]/.next-internal/server/app/api/trips/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/supabase/server.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
;
;
async function createClient() {
    const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://ocyjxnddxuhhlnguybre.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9jeWp4bmRkeHVoaGxuZ3V5YnJlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE5NDY2ODYsImV4cCI6MjA2NzUyMjY4Nn0.LQJjfsT5lKAnfD7dvWMtKMjgLiFo-vjcone8yi-Gm40"), {
        cookies: {
            getAll () {
                return cookieStore.getAll();
            },
            setAll (cookiesToSet) {
                try {
                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));
                } catch  {
                // The `setAll` method was called from a Server Component.
                // This can be ignored if you have middleware refreshing
                // user sessions.
                }
            }
        }
    });
}
}}),
"[project]/src/lib/security/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Security Configuration
 * Provides different security levels for development and production environments
 */ __turbopack_context__.s({
    "getSecurityConfig": (()=>getSecurityConfig),
    "getSecurityLevel": (()=>getSecurityLevel),
    "isAuditLoggingEnabled": (()=>isAuditLoggingEnabled),
    "isCORSEnabled": (()=>isCORSEnabled),
    "isCSRFProtectionEnabled": (()=>isCSRFProtectionEnabled),
    "isDevelopment": (()=>isDevelopment),
    "isEmailVerificationRequired": (()=>isEmailVerificationRequired),
    "isProduction": (()=>isProduction),
    "isRateLimitingEnabled": (()=>isRateLimitingEnabled),
    "isRelaxedMode": (()=>isRelaxedMode),
    "isStrictMode": (()=>isStrictMode),
    "securityConfig": (()=>securityConfig),
    "switchToRelaxedMode": (()=>switchToRelaxedMode),
    "switchToStrictMode": (()=>switchToStrictMode),
    "updateSecurityConfig": (()=>updateSecurityConfig),
    "validateSecurityConfig": (()=>validateSecurityConfig)
});
// Development configuration - relaxed security for easier development
const developmentConfig = {
    auth: {
        requireEmailVerification: false,
        sessionTimeout: 24 * 60 * 60 * 1000,
        maxFailedAttempts: 10,
        lockoutDuration: 5 * 60 * 1000,
        passwordMinLength: 6,
        requireStrongPassword: false,
        enableMFA: false
    },
    rateLimit: {
        enabled: true,
        auth: {
            requests: 20,
            window: 15 * 60 * 1000 // 15 minutes
        },
        api: {
            requests: 200,
            window: 60 * 1000 // 1 minute
        },
        strict: {
            requests: 50,
            window: 60 * 1000
        }
    },
    cors: {
        enabled: true,
        allowedOrigins: [
            'http://localhost:3000',
            'http://127.0.0.1:3000'
        ],
        allowedMethods: [
            'GET',
            'POST',
            'PUT',
            'DELETE',
            'PATCH',
            'OPTIONS'
        ],
        allowCredentials: true
    },
    headers: {
        enableCSP: false,
        enableHSTS: false,
        enableXFrameOptions: true,
        enableXContentTypeOptions: true,
        enableReferrerPolicy: true
    },
    validation: {
        strictMode: false,
        maxRequestSize: 10 * 1024 * 1024,
        sanitizeInput: true
    },
    logging: {
        enableAuditLog: true,
        logLevel: 'debug',
        enableSecurityEvents: true
    },
    csrf: {
        enabled: false,
        cookieName: '_csrf',
        headerName: 'x-csrf-token'
    }
};
// Production configuration - strict security
const productionConfig = {
    auth: {
        requireEmailVerification: true,
        sessionTimeout: 8 * 60 * 60 * 1000,
        maxFailedAttempts: 5,
        lockoutDuration: 30 * 60 * 1000,
        passwordMinLength: 12,
        requireStrongPassword: true,
        enableMFA: true
    },
    rateLimit: {
        enabled: true,
        auth: {
            requests: 5,
            window: 15 * 60 * 1000 // 15 minutes
        },
        api: {
            requests: 100,
            window: 60 * 1000 // 1 minute
        },
        strict: {
            requests: 20,
            window: 60 * 1000
        }
    },
    cors: {
        enabled: true,
        allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') || [],
        allowedMethods: [
            'GET',
            'POST',
            'PUT',
            'DELETE',
            'PATCH'
        ],
        allowCredentials: true
    },
    headers: {
        enableCSP: true,
        enableHSTS: true,
        enableXFrameOptions: true,
        enableXContentTypeOptions: true,
        enableReferrerPolicy: true
    },
    validation: {
        strictMode: true,
        maxRequestSize: 5 * 1024 * 1024,
        sanitizeInput: true
    },
    logging: {
        enableAuditLog: true,
        logLevel: 'warn',
        enableSecurityEvents: true
    },
    csrf: {
        enabled: true,
        cookieName: '_csrf',
        headerName: 'x-csrf-token'
    }
};
function getSecurityConfig() {
    const env = ("TURBOPACK compile-time value", "development") || 'development';
    const securityMode = process.env.SECURITY_MODE;
    // Base configuration
    let config;
    if (env === 'production') {
        config = {
            ...productionConfig
        };
    } else {
        config = {
            ...developmentConfig
        };
    }
    // Allow environment variable overrides
    if (securityMode === 'strict') {
        config = {
            ...productionConfig
        };
    } else if (securityMode === 'relaxed') {
        config = {
            ...developmentConfig
        };
    }
    // Individual feature overrides via environment variables
    if (process.env.DISABLE_RATE_LIMITING === 'true') {
        config.rateLimit.enabled = false;
    }
    if (process.env.DISABLE_CSRF === 'true') {
        config.csrf.enabled = false;
    }
    if (process.env.DISABLE_EMAIL_VERIFICATION === 'true') {
        config.auth.requireEmailVerification = false;
    }
    if (process.env.ENABLE_DEBUG_LOGGING === 'true') {
        config.logging.logLevel = 'debug';
    }
    if (process.env.CORS_ORIGINS) {
        config.cors.allowedOrigins = process.env.CORS_ORIGINS.split(',');
    }
    return config;
}
function isProduction() {
    return ("TURBOPACK compile-time value", "development") === 'production';
}
function isDevelopment() {
    return ("TURBOPACK compile-time value", "development") === 'development';
}
function isStrictMode() {
    return process.env.SECURITY_MODE === 'strict' || isProduction();
}
function isRelaxedMode() {
    return process.env.SECURITY_MODE === 'relaxed' || isDevelopment() && !isStrictMode();
}
function getSecurityLevel() {
    if (isStrictMode()) return 'strict';
    if (isRelaxedMode()) return 'relaxed';
    return 'normal';
}
function isRateLimitingEnabled() {
    return securityConfig.rateLimit.enabled && process.env.DISABLE_RATE_LIMITING !== 'true';
}
function isCSRFProtectionEnabled() {
    return securityConfig.csrf.enabled && process.env.DISABLE_CSRF !== 'true';
}
function isEmailVerificationRequired() {
    return securityConfig.auth.requireEmailVerification && process.env.DISABLE_EMAIL_VERIFICATION !== 'true';
}
function isCORSEnabled() {
    return securityConfig.cors.enabled;
}
function isAuditLoggingEnabled() {
    return securityConfig.logging.enableAuditLog;
}
function updateSecurityConfig(updates) {
    Object.assign(securityConfig, updates);
    console.log('Security configuration updated:', {
        level: getSecurityLevel(),
        timestamp: new Date().toISOString()
    });
}
function switchToStrictMode() {
    if (!isProduction()) {
        process.env.SECURITY_MODE = 'strict';
        updateSecurityConfig(productionConfig);
        console.warn('Switched to strict security mode');
    }
}
function switchToRelaxedMode() {
    if (!isProduction()) {
        process.env.SECURITY_MODE = 'relaxed';
        updateSecurityConfig(developmentConfig);
        console.warn('Switched to relaxed security mode');
    }
}
function validateSecurityConfig() {
    const errors = [];
    const config = securityConfig;
    // Validate rate limiting
    if (config.rateLimit.enabled) {
        if (config.rateLimit.auth.requests <= 0) {
            errors.push('Auth rate limit requests must be positive');
        }
        if (config.rateLimit.api.requests <= 0) {
            errors.push('API rate limit requests must be positive');
        }
    }
    // Validate authentication
    if (config.auth.sessionTimeout <= 0) {
        errors.push('Session timeout must be positive');
    }
    if (config.auth.passwordMinLength < 1) {
        errors.push('Password minimum length must be at least 1');
    }
    // Validate CORS
    if (config.cors.enabled && config.cors.allowedOrigins.length === 0) {
        errors.push('CORS enabled but no allowed origins specified');
    }
    // Production-specific validations
    if (isProduction()) {
        "TURBOPACK unreachable";
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
const securityConfig = getSecurityConfig();
// Log current security configuration on startup
console.log('Security Configuration Loaded:', {
    environment: ("TURBOPACK compile-time value", "development"),
    securityMode: process.env.SECURITY_MODE || 'default',
    level: getSecurityLevel(),
    features: {
        rateLimiting: isRateLimitingEnabled(),
        csrfProtection: isCSRFProtectionEnabled(),
        emailVerification: isEmailVerificationRequired(),
        cors: isCORSEnabled(),
        auditLogging: isAuditLoggingEnabled()
    }
});
}}),
"[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Enhanced rate limiting with sliding window and performance optimizations
// In production, consider using Redis for distributed rate limiting
__turbopack_context__.s({
    "rateLimit": (()=>rateLimit),
    "rateLimitByIP": (()=>rateLimitByIP),
    "rateLimitByUser": (()=>rateLimitByUser),
    "rateLimitConfigs": (()=>rateLimitConfigs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/config.ts [app-route] (ecmascript)");
;
// Use a more efficient cache structure
const rateLimitCache = {
    store: new Map(),
    lastCleanup: Date.now()
};
async function rateLimit(identifier, requests = 100, windowMs = 3600000) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].rateLimit.enabled) {
        return {
            success: true
        };
    }
    const windowMilliseconds = typeof windowMs === 'string' ? parseTimeWindow(windowMs) : windowMs;
    const now = Date.now();
    // Periodic cleanup to prevent memory leaks
    if (now - rateLimitCache.lastCleanup > 300000) {
        cleanupOldEntries(now);
        rateLimitCache.lastCleanup = now;
    }
    const entry = rateLimitCache.store.get(identifier);
    if (!entry) {
        rateLimitCache.store.set(identifier, {
            count: 1,
            resetTime: now + windowMilliseconds,
            timestamps: [
                now
            ]
        });
        return {
            success: true
        };
    }
    // Sliding window algorithm - remove old timestamps
    entry.timestamps = entry.timestamps.filter((timestamp)=>now - timestamp < windowMilliseconds);
    if (entry.timestamps.length >= requests) {
        const oldestTimestamp = Math.min(...entry.timestamps);
        const retryAfter = Math.ceil((oldestTimestamp + windowMilliseconds - now) / 1000);
        return {
            success: false,
            retryAfter: Math.max(retryAfter, 1)
        };
    }
    // Add current timestamp
    entry.timestamps.push(now);
    entry.count = entry.timestamps.length;
    entry.resetTime = now + windowMilliseconds;
    return {
        success: true
    };
}
function parseTimeWindow(window) {
    const match = window.match(/^(\d+)([smhd])$/);
    if (!match) return 3600000 // Default to 1 hour
    ;
    const value = parseInt(match[1]);
    const unit = match[2];
    switch(unit){
        case 's':
            return value * 1000;
        case 'm':
            return value * 60 * 1000;
        case 'h':
            return value * 60 * 60 * 1000;
        case 'd':
            return value * 24 * 60 * 60 * 1000;
        default:
            return 3600000;
    }
}
function cleanupOldEntries(now) {
    // Clean up entries older than 24 hours or with no recent activity
    const cutoff = now - 24 * 60 * 60 * 1000;
    const maxEntries = 10000 // Prevent memory bloat
    ;
    let entriesRemoved = 0;
    for (const [key, entry] of rateLimitCache.store.entries()){
        if (entry.resetTime < cutoff || entriesRemoved > maxEntries) {
            rateLimitCache.store.delete(key);
            entriesRemoved++;
        }
    }
    // If still too many entries, remove oldest ones
    if (rateLimitCache.store.size > maxEntries) {
        const entries = Array.from(rateLimitCache.store.entries()).sort(([, a], [, b])=>a.resetTime - b.resetTime);
        const toRemove = entries.slice(0, entries.length - maxEntries);
        toRemove.forEach(([key])=>rateLimitCache.store.delete(key));
    }
}
const rateLimitConfigs = {
    // Authentication endpoints - use config values
    auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].rateLimit.auth,
    // API endpoints - use config values
    api: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].rateLimit.api,
    // Strict endpoints - use config values
    strict: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].rateLimit.strict,
    // File upload endpoints
    upload: {
        requests: 10,
        window: 60 * 60 * 1000
    },
    // Recipe import (external API calls)
    import: {
        requests: 20,
        window: 60 * 60 * 1000
    },
    // Chat/AI endpoints
    chat: {
        requests: 50,
        window: 60 * 60 * 1000
    },
    // Search endpoints
    search: {
        requests: 200,
        window: 60 * 60 * 1000
    } // 1 hour
};
async function rateLimitByIP(request, config = rateLimitConfigs.api) {
    const ip = getClientIP(request);
    return rateLimit(`ip:${ip}`, config.requests, config.window);
}
async function rateLimitByUser(userId, config = rateLimitConfigs.api) {
    return rateLimit(`user:${userId}`, config.requests, config.window);
}
function getClientIP(request) {
    // Try to get IP from various headers
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
        return realIP;
    }
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
        return cfConnectingIP;
    }
    return 'unknown';
}
}}),
"[project]/src/lib/security/validation.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "patterns": (()=>patterns),
    "sanitizeHTML": (()=>sanitizeHTML),
    "validateAndSanitizeInput": (()=>validateAndSanitizeInput),
    "validateEmail": (()=>validateEmail),
    "validateInput": (()=>validateInput),
    "validateSecureField": (()=>validateSecureField),
    "validateURL": (()=>validateURL),
    "validateUUID": (()=>validateUUID)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/config.ts [app-route] (ecmascript)");
;
async function validateInput(request, schemas) {
    const errors = [];
    const validatedData = {};
    try {
        // Validate request body
        if (schemas.body) {
            let body = {};
            if (request.method !== 'GET' && request.method !== 'DELETE') {
                try {
                    body = await request.json();
                } catch (error) {
                    errors.push('Invalid JSON in request body');
                    return {
                        success: false,
                        errors
                    };
                }
            }
            const bodyValidation = validateObject(body, schemas.body, 'body');
            if (!bodyValidation.success) {
                errors.push(...bodyValidation.errors);
            } else {
                validatedData.body = bodyValidation.data;
            }
        }
        // Validate query parameters
        if (schemas.query) {
            const searchParams = request.nextUrl.searchParams;
            const query = {};
            for (const [key, value] of searchParams.entries()){
                query[key] = value;
            }
            const queryValidation = validateObject(query, schemas.query, 'query');
            if (!queryValidation.success) {
                errors.push(...queryValidation.errors);
            } else {
                validatedData.query = queryValidation.data;
            }
        }
        return {
            success: errors.length === 0,
            errors: errors.length > 0 ? errors : undefined,
            data: validatedData
        };
    } catch (error) {
        console.error('Validation error:', error);
        return {
            success: false,
            errors: [
                'Validation failed'
            ]
        };
    }
}
function validateObject(obj, schema, prefix = '') {
    const errors = [];
    const validatedData = {};
    for (const [key, rules] of Object.entries(schema)){
        const fieldPath = prefix ? `${prefix}.${key}` : key;
        const value = obj[key];
        // Check required fields
        if (rules.required && (value === undefined || value === null || value === '')) {
            errors.push(`${fieldPath} is required`);
            continue;
        }
        // Skip validation if field is not required and not provided
        if (!rules.required && (value === undefined || value === null)) {
            continue;
        }
        // Type validation
        if (rules.type) {
            const typeValidation = validateType(value, rules.type, fieldPath);
            if (!typeValidation.success) {
                errors.push(...typeValidation.errors);
                continue;
            }
        }
        // String validations
        if (rules.type === 'string' && typeof value === 'string') {
            if (rules.minLength && value.length < rules.minLength) {
                errors.push(`${fieldPath} must be at least ${rules.minLength} characters`);
                continue;
            }
            if (rules.maxLength && value.length > rules.maxLength) {
                errors.push(`${fieldPath} must be no more than ${rules.maxLength} characters`);
                continue;
            }
            if (rules.pattern && !rules.pattern.test(value)) {
                errors.push(`${fieldPath} format is invalid`);
                continue;
            }
            if (rules.enum && !rules.enum.includes(value)) {
                errors.push(`${fieldPath} must be one of: ${rules.enum.join(', ')}`);
                continue;
            }
        }
        // Number validations
        if (rules.type === 'number' && typeof value === 'number') {
            if (rules.min !== undefined && value < rules.min) {
                errors.push(`${fieldPath} must be at least ${rules.min}`);
                continue;
            }
            if (rules.max !== undefined && value > rules.max) {
                errors.push(`${fieldPath} must be no more than ${rules.max}`);
                continue;
            }
        }
        // Array validations
        if (rules.type === 'array' && Array.isArray(value)) {
            if (rules.maxItems && value.length > rules.maxItems) {
                errors.push(`${fieldPath} must have no more than ${rules.maxItems} items`);
                continue;
            }
        }
        // Custom validation
        if (rules.custom) {
            const customResult = rules.custom(value);
            if (customResult !== true) {
                errors.push(typeof customResult === 'string' ? customResult : `${fieldPath} is invalid`);
                continue;
            }
        }
        // Sanitize and add to validated data
        validatedData[key] = sanitizeValue(value, rules.type);
    }
    return {
        success: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
        data: validatedData
    };
}
function validateType(value, expectedType, fieldPath) {
    switch(expectedType){
        case 'string':
            if (typeof value !== 'string') {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a string`
                    ]
                };
            }
            break;
        case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a number`
                    ]
                };
            }
            break;
        case 'boolean':
            if (typeof value !== 'boolean') {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be a boolean`
                    ]
                };
            }
            break;
        case 'array':
            if (!Array.isArray(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be an array`
                    ]
                };
            }
            break;
        case 'object':
            if (typeof value !== 'object' || value === null || Array.isArray(value)) {
                return {
                    success: false,
                    errors: [
                        `${fieldPath} must be an object`
                    ]
                };
            }
            break;
    }
    return {
        success: true
    };
}
function sanitizeValue(value, type) {
    if (value === null || value === undefined) {
        return value;
    }
    switch(type){
        case 'string':
            let sanitized = typeof value === 'string' ? value : String(value);
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].validation.sanitizeInput) {
                // Enhanced sanitization for security
                sanitized = sanitizeForSQLInjection(sanitized);
                sanitized = sanitizeForXSS(sanitized);
                sanitized = sanitizeForPathTraversal(sanitized);
            }
            return sanitized.trim().slice(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].validation.maxRequestSize / 1000);
        case 'number':
            const num = typeof value === 'number' ? value : Number(value);
            // Prevent extremely large numbers that could cause issues
            return isFinite(num) ? Math.max(-1e15, Math.min(1e15, num)) : 0;
        case 'boolean':
            return Boolean(value);
        case 'array':
            const arr = Array.isArray(value) ? value : [
                value
            ];
            return arr.slice(0, 1000).map((item)=>sanitizeValue(item, 'string'));
        case 'object':
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                const sanitizedObj = {};
                for (const [key, val] of Object.entries(value)){
                    if (typeof key === 'string' && key.length < 100) {
                        sanitizedObj[sanitizeValue(key, 'string')] = sanitizeValue(val);
                    }
                }
                return sanitizedObj;
            }
            return {};
        default:
            return value;
    }
}
// SQL Injection prevention
function sanitizeForSQLInjection(input) {
    if (!input) return input;
    // Common SQL injection patterns
    const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)/gi,
        /(--|\/\*|\*\/|;|'|"|`)/g,
        /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
        /(\bOR\b|\bAND\b)\s+['"]?\w+['"]?\s*=\s*['"]?\w+['"]?/gi,
        /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/gi
    ];
    let sanitized = input;
    sqlPatterns.forEach((pattern)=>{
        sanitized = sanitized.replace(pattern, '');
    });
    return sanitized;
}
// XSS prevention
function sanitizeForXSS(input) {
    if (!input) return input;
    return input.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;').replace(/javascript:/gi, '').replace(/on\w+\s*=/gi, '').replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
}
// Path traversal prevention
function sanitizeForPathTraversal(input) {
    if (!input) return input;
    return input.replace(/\.\./g, '').replace(/[\/\\]/g, '').replace(/\0/g, '');
}
function validateSecureField(value, fieldType) {
    const errors = [];
    if (!value || typeof value !== 'string') {
        errors.push('Field is required and must be a string');
        return {
            isValid: false,
            errors
        };
    }
    // Check for suspicious patterns
    const suspiciousPatterns = [
        /[\x00-\x1f\x7f-\x9f]/g,
        /[<>]/g,
        /javascript:/gi,
        /data:/gi,
        /vbscript:/gi
    ];
    for (const pattern of suspiciousPatterns){
        if (pattern.test(value)) {
            errors.push('Field contains invalid characters');
            break;
        }
    }
    // Field-specific validation
    switch(fieldType){
        case 'password':
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].auth.requireStrongPassword) {
                if (value.length < __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].auth.passwordMinLength) {
                    errors.push(`Password must be at least ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].auth.passwordMinLength} characters`);
                }
                if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/.test(value)) {
                    errors.push('Password must contain uppercase, lowercase, number, and special character');
                }
            }
            break;
        case 'email':
            if (!patterns.email.test(value)) {
                errors.push('Invalid email format');
            }
            break;
        case 'username':
            if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
                errors.push('Username can only contain letters, numbers, underscores, and hyphens');
            }
            if (value.length < 3 || value.length > 30) {
                errors.push('Username must be between 3 and 30 characters');
            }
            break;
        case 'filename':
            if (!/^[a-zA-Z0-9._-]+$/.test(value)) {
                errors.push('Filename contains invalid characters');
            }
            if (value.length > 255) {
                errors.push('Filename too long');
            }
            break;
    }
    return {
        isValid: errors.length === 0,
        errors
    };
}
const patterns = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    url: /^https?:\/\/.+/,
    uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
    slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
    phone: /^\+?[\d\s\-\(\)]+$/,
    date: /^\d{4}-\d{2}-\d{2}$/,
    time: /^\d{2}:\d{2}$/,
    datetime: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/
};
function validateEmail(email) {
    return patterns.email.test(email);
}
function validateURL(url) {
    return patterns.url.test(url);
}
function validateUUID(uuid) {
    return patterns.uuid.test(uuid);
}
function sanitizeHTML(input) {
    return input.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
}
function validateAndSanitizeInput(input, schema) {
    const validation = validateObject(input, schema);
    return {
        isValid: validation.success,
        sanitized: validation.data || {},
        errors: validation.errors || []
    };
}
}}),
"[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "buildErrorContext": (()=>buildErrorContext),
    "createAuthError": (()=>createAuthError),
    "createForbiddenError": (()=>createForbiddenError),
    "createRateLimitError": (()=>createRateLimitError),
    "createSecurityError": (()=>createSecurityError),
    "createValidationError": (()=>createValidationError),
    "handleSecureError": (()=>handleSecureError),
    "withErrorHandling": (()=>withErrorHandling)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
;
// Enhanced secure error mappings with severity and category
const ERROR_MAPPINGS = {
    // Authentication errors
    'PGRST301': {
        code: 'AUTH_REQUIRED',
        message: 'Authentication required',
        statusCode: 401,
        severity: 'medium',
        category: 'auth'
    },
    'PGRST302': {
        code: 'FORBIDDEN',
        message: 'Access denied',
        statusCode: 403,
        severity: 'high',
        category: 'auth'
    },
    'AUTH_FAILED': {
        code: 'AUTH_FAILED',
        message: 'Authentication failed',
        statusCode: 401,
        severity: 'medium',
        category: 'auth'
    },
    'SESSION_EXPIRED': {
        code: 'SESSION_EXPIRED',
        message: 'Session expired',
        statusCode: 401,
        severity: 'low',
        category: 'auth'
    },
    'EMAIL_NOT_VERIFIED': {
        code: 'EMAIL_NOT_VERIFIED',
        message: 'Email verification required',
        statusCode: 403,
        severity: 'medium',
        category: 'auth'
    },
    'ACCOUNT_SUSPENDED': {
        code: 'ACCOUNT_SUSPENDED',
        message: 'Account suspended',
        statusCode: 403,
        severity: 'high',
        category: 'auth'
    },
    // Database constraint errors
    '23503': {
        code: 'INVALID_REFERENCE',
        message: 'Invalid data reference',
        statusCode: 400,
        severity: 'low',
        category: 'validation'
    },
    '23505': {
        code: 'DUPLICATE_ENTRY',
        message: 'Duplicate entry not allowed',
        statusCode: 409,
        severity: 'low',
        category: 'validation'
    },
    '23514': {
        code: 'VALIDATION_ERROR',
        message: 'Data validation failed',
        statusCode: 400,
        severity: 'low',
        category: 'validation'
    },
    // Rate limiting
    'RATE_LIMIT': {
        code: 'RATE_LIMIT',
        message: 'Too many requests',
        statusCode: 429,
        severity: 'medium',
        category: 'rate_limit'
    },
    // Security errors
    'CSRF_ERROR': {
        code: 'CSRF_ERROR',
        message: 'CSRF token validation failed',
        statusCode: 403,
        severity: 'high',
        category: 'security'
    },
    'SUSPICIOUS_REQUEST': {
        code: 'SUSPICIOUS_REQUEST',
        message: 'Request blocked',
        statusCode: 403,
        severity: 'high',
        category: 'security'
    },
    'SQL_INJECTION_ATTEMPT': {
        code: 'SECURITY_VIOLATION',
        message: 'Request blocked',
        statusCode: 403,
        severity: 'critical',
        category: 'security'
    },
    'XSS_ATTEMPT': {
        code: 'SECURITY_VIOLATION',
        message: 'Request blocked',
        statusCode: 403,
        severity: 'critical',
        category: 'security'
    },
    // Validation errors
    'VALIDATION_ERROR': {
        code: 'VALIDATION_ERROR',
        message: 'Invalid input data',
        statusCode: 400,
        severity: 'low',
        category: 'validation'
    },
    'REQUEST_TOO_LARGE': {
        code: 'REQUEST_TOO_LARGE',
        message: 'Request size exceeds limit',
        statusCode: 413,
        severity: 'medium',
        category: 'validation'
    },
    'INVALID_CONTENT_TYPE': {
        code: 'INVALID_CONTENT_TYPE',
        message: 'Invalid content type',
        statusCode: 415,
        severity: 'low',
        category: 'validation'
    },
    // System errors
    'INTERNAL_ERROR': {
        code: 'INTERNAL_ERROR',
        message: 'An unexpected error occurred',
        statusCode: 500,
        severity: 'high',
        category: 'system'
    },
    'SERVICE_UNAVAILABLE': {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Service temporarily unavailable',
        statusCode: 503,
        severity: 'high',
        category: 'system'
    },
    'DATABASE_ERROR': {
        code: 'DATABASE_ERROR',
        message: 'Database operation failed',
        statusCode: 500,
        severity: 'high',
        category: 'database'
    }
};
function handleSecureError(error, customMessage, context) {
    const errorId = __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomUUID();
    const timestamp = new Date().toISOString();
    // Determine the error type
    let secureError = determineErrorType(error);
    // Override message if provided
    if (customMessage) {
        secureError = {
            ...secureError,
            message: customMessage
        };
    }
    // Enhanced error logging with context
    const errorLog = {
        errorId,
        timestamp,
        error: {
            name: error.name,
            message: error.message,
            code: error.code,
            stack: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.logLevel === 'debug' ? error.stack : undefined,
            details: error.details
        },
        secureError,
        context: {
            userId: context?.userId,
            ip: context?.ip,
            userAgent: context?.userAgent,
            endpoint: context?.endpoint,
            method: context?.method,
            requestId: context?.requestId,
            sessionId: context?.sessionId
        },
        severity: secureError.severity,
        category: secureError.category
    };
    // Log based on severity and configuration
    logError(errorLog);
    // Send alerts for critical errors
    if (secureError.severity === 'critical') {
        sendCriticalErrorAlert(errorLog);
    }
    // Prepare response
    const responseBody = {
        error: {
            code: secureError.code,
            message: secureError.message,
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.logLevel === 'debug' && {
                errorId
            }
        }
    };
    // Add additional debug info in development
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.logLevel === 'debug' && ("TURBOPACK compile-time value", "development") === 'development') {
        responseBody.debug = {
            originalError: error.message,
            timestamp,
            category: secureError.category,
            severity: secureError.severity
        };
    }
    const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(responseBody, {
        status: secureError.statusCode
    });
    // Add security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    return response;
}
function determineErrorType(error) {
    // Check for exact code match
    if (error.code && ERROR_MAPPINGS[error.code]) {
        return ERROR_MAPPINGS[error.code];
    }
    // Check for error name match
    if (error.name && ERROR_MAPPINGS[error.name]) {
        return ERROR_MAPPINGS[error.name];
    }
    // Pattern matching for common error types
    const message = error.message?.toLowerCase() || '';
    if (message.includes('rate limit') || message.includes('too many requests')) {
        return ERROR_MAPPINGS['RATE_LIMIT'];
    }
    if (message.includes('validation') || message.includes('invalid')) {
        return ERROR_MAPPINGS['VALIDATION_ERROR'];
    }
    if (message.includes('auth') || message.includes('unauthorized')) {
        return ERROR_MAPPINGS['AUTH_REQUIRED'];
    }
    if (message.includes('forbidden') || message.includes('access denied')) {
        return ERROR_MAPPINGS['PGRST302'];
    }
    if (message.includes('duplicate') || message.includes('already exists')) {
        return ERROR_MAPPINGS['23505'];
    }
    // Security pattern detection
    if (detectSQLInjection(message)) {
        return ERROR_MAPPINGS['SQL_INJECTION_ATTEMPT'];
    }
    if (detectXSSAttempt(message)) {
        return ERROR_MAPPINGS['XSS_ATTEMPT'];
    }
    // Database errors
    if (message.includes('database') || message.includes('connection') || error.code?.startsWith('23')) {
        return ERROR_MAPPINGS['DATABASE_ERROR'];
    }
    // Default to internal error
    return ERROR_MAPPINGS['INTERNAL_ERROR'];
}
function detectSQLInjection(message) {
    const sqlPatterns = [
        /union.*select/i,
        /drop.*table/i,
        /insert.*into/i,
        /delete.*from/i,
        /update.*set/i,
        /exec.*xp_/i,
        /information_schema/i,
        /sysobjects/i,
        /syscolumns/i
    ];
    return sqlPatterns.some((pattern)=>pattern.test(message));
}
function detectXSSAttempt(message) {
    const xssPatterns = [
        /<script/i,
        /javascript:/i,
        /vbscript:/i,
        /onload=/i,
        /onerror=/i,
        /onclick=/i,
        /eval\(/i,
        /expression\(/i
    ];
    return xssPatterns.some((pattern)=>pattern.test(message));
}
function logError(errorLog) {
    const { severity, category } = errorLog.secureError;
    // Determine log level based on severity
    switch(severity){
        case 'critical':
            console.error('🚨 CRITICAL ERROR:', errorLog);
            break;
        case 'high':
            console.error('❌ HIGH SEVERITY ERROR:', errorLog);
            break;
        case 'medium':
            console.warn('⚠️ MEDIUM SEVERITY ERROR:', errorLog);
            break;
        case 'low':
            if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.logLevel === 'debug') {
                console.info('ℹ️ LOW SEVERITY ERROR:', errorLog);
            }
            break;
        default:
            console.error('ERROR:', errorLog);
    }
    // Log to external monitoring service if configured
    if (process.env.SENTRY_DSN && (severity === 'critical' || severity === 'high')) {
        // In a real implementation, you would send to Sentry here
        console.log('Would send to Sentry:', errorLog.errorId);
    }
    // Store in audit log for security events
    if (category === 'security' || category === 'auth') {
        storeSecurityAuditLog(errorLog);
    }
}
function sendCriticalErrorAlert(errorLog) {
    // In production, this would send alerts via email, Slack, PagerDuty, etc.
    console.error('🚨 CRITICAL ERROR ALERT 🚨', {
        errorId: errorLog.errorId,
        message: errorLog.secureError.message,
        category: errorLog.secureError.category,
        timestamp: errorLog.timestamp,
        context: errorLog.context
    });
    // Example: Send to monitoring service
    if (process.env.WEBHOOK_URL) {
        // In a real implementation, you would send a webhook here
        console.log('Would send webhook alert for:', errorLog.errorId);
    }
}
function storeSecurityAuditLog(errorLog) {
    // In a real implementation, this would store in a secure audit log database
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.enableAuditLog) {
        console.log('SECURITY AUDIT LOG:', {
            type: 'error',
            errorId: errorLog.errorId,
            category: errorLog.secureError.category,
            severity: errorLog.secureError.severity,
            userId: errorLog.context.userId,
            ip: errorLog.context.ip,
            endpoint: errorLog.context.endpoint,
            timestamp: errorLog.timestamp
        });
    }
}
function createValidationError(message, details) {
    const error = new Error(message);
    error.name = 'VALIDATION_ERROR';
    if (details) {
        error.details = details;
    }
    return error;
}
function createAuthError(message = 'Authentication required', code) {
    const error = new Error(message);
    error.name = code || 'AUTH_REQUIRED';
    return error;
}
function createForbiddenError(message = 'Access denied') {
    const error = new Error(message);
    error.name = 'FORBIDDEN';
    return error;
}
function createSecurityError(message, type) {
    const error = new Error(message);
    error.name = type;
    return error;
}
function createRateLimitError(retryAfter) {
    const error = new Error('Too many requests');
    error.name = 'RATE_LIMIT';
    if (retryAfter) {
        error.retryAfter = retryAfter;
    }
    return error;
}
function buildErrorContext(request, userId) {
    if (!request) {
        return {
            timestamp: new Date().toISOString(),
            requestId: __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomUUID()
        };
    }
    return {
        userId,
        ip: getClientIP(request),
        userAgent: request.headers.get('user-agent') || undefined,
        endpoint: request.nextUrl.pathname,
        method: request.method,
        timestamp: new Date().toISOString(),
        requestId: __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomUUID(),
        sessionId: request.cookies.get('session')?.value
    };
}
function getClientIP(request) {
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
        return realIP;
    }
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
        return cfConnectingIP;
    }
    return 'unknown';
}
function withErrorHandling(handler, context) {
    return async (...args)=>{
        try {
            return await handler(...args);
        } catch (error) {
            throw handleSecureError(error, undefined, context);
        }
    };
}
}}),
"[project]/src/lib/security/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addSecurityHeaders": (()=>addSecurityHeaders),
    "generateCSRFToken": (()=>generateCSRFToken),
    "getClientIP": (()=>getClientIP),
    "logSecurityEvent": (()=>logSecurityEvent),
    "sanitizeArray": (()=>sanitizeArray),
    "sanitizeNumber": (()=>sanitizeNumber),
    "sanitizeObject": (()=>sanitizeObject),
    "sanitizeString": (()=>sanitizeString),
    "validateCSRFToken": (()=>validateCSRFToken),
    "validateRequestSize": (()=>validateRequestSize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/crypto [external] (crypto, cjs)");
;
;
function addSecurityHeaders(response) {
    const config = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].headers;
    // Always set basic security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-DNS-Prefetch-Control', 'off');
    response.headers.set('X-Download-Options', 'noopen');
    if (config.enableXFrameOptions) {
        response.headers.set('X-Frame-Options', 'DENY');
    }
    if (config.enableReferrerPolicy) {
        response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    }
    // Enhanced permissions policy
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()');
    // Content Security Policy
    if (config.enableCSP) {
        const cspDirectives = [
            "default-src 'self'",
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://maps.googleapis.com",
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
            "img-src 'self' data: https: blob:",
            "font-src 'self' data: https://fonts.gstatic.com",
            "connect-src 'self' https://api.openai.com https://maps.googleapis.com",
            "frame-src 'none'",
            "object-src 'none'",
            "base-uri 'self'",
            "form-action 'self'"
        ];
        response.headers.set('Content-Security-Policy', cspDirectives.join('; '));
    }
    // HSTS for HTTPS
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    // Remove server information
    response.headers.delete('Server');
    response.headers.delete('X-Powered-By');
    response.headers.delete('X-AspNet-Version');
    response.headers.delete('X-AspNetMvc-Version');
    // Add cache control for sensitive endpoints
    if (response.url?.includes('/api/auth/') || response.url?.includes('/api/user/')) {
        response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
        response.headers.set('Pragma', 'no-cache');
        response.headers.set('Expires', '0');
    }
    return response;
}
function generateCSRFToken() {
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].randomBytes(32).toString('hex');
}
function validateCSRFToken(request, sessionToken) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].csrf.enabled) {
        return true;
    }
    const headerToken = request.headers.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].csrf.headerName);
    const cookieToken = request.cookies.get(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].csrf.cookieName)?.value;
    if (!headerToken || !cookieToken || !sessionToken) {
        return false;
    }
    // Use timing-safe comparison
    return __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(Buffer.from(headerToken, 'hex'), Buffer.from(cookieToken, 'hex')) && __TURBOPACK__imported__module__$5b$externals$5d2f$crypto__$5b$external$5d$__$28$crypto$2c$__cjs$29$__["default"].timingSafeEqual(Buffer.from(cookieToken, 'hex'), Buffer.from(sessionToken, 'hex'));
}
function sanitizeString(input, options = {}) {
    if (!input || typeof input !== 'string') return '';
    const { maxLength = 10000, allowHTML = false, allowSpecialChars = true } = options;
    let sanitized = input.trim();
    if (!allowHTML) {
        // Enhanced HTML sanitization
        sanitized = sanitized.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;').replace(/javascript:/gi, '').replace(/data:/gi, '').replace(/vbscript:/gi, '').replace(/on\w+\s*=/gi, '');
    }
    if (!allowSpecialChars) {
        // Remove special characters that could be used for injection
        sanitized = sanitized.replace(/[^\w\s\-_.@]/g, '');
    }
    // Remove null bytes and control characters
    sanitized = sanitized.replace(/[\x00-\x1f\x7f-\x9f]/g, '');
    return sanitized.slice(0, maxLength);
}
function sanitizeNumber(input, options = {}) {
    const { min = -Infinity, max = Infinity, integer = false } = options;
    let num = Number(input);
    if (isNaN(num) || !isFinite(num)) return null;
    if (integer) {
        num = Math.floor(num);
    }
    return Math.max(min, Math.min(max, num));
}
function sanitizeArray(input, options = {}) {
    const { maxLength = 100, itemSanitizer } = options;
    if (!Array.isArray(input)) return [];
    let sanitized = input.slice(0, maxLength);
    if (itemSanitizer) {
        sanitized = sanitized.map(itemSanitizer);
    }
    return sanitized;
}
function sanitizeObject(input, allowedKeys, options = {}) {
    const { deep = false, sanitizeValues = true } = options;
    if (typeof input !== 'object' || input === null || Array.isArray(input)) return {};
    const sanitized = {};
    for (const key of allowedKeys){
        if (key in input) {
            let value = input[key];
            if (sanitizeValues) {
                if (typeof value === 'string') {
                    value = sanitizeString(value);
                } else if (typeof value === 'number') {
                    value = sanitizeNumber(value);
                } else if (Array.isArray(value)) {
                    value = sanitizeArray(value);
                } else if (deep && typeof value === 'object' && value !== null) {
                    value = sanitizeObject(value, Object.keys(value), options);
                }
            }
            sanitized[key] = value;
        }
    }
    return sanitized;
}
function validateRequestSize(request) {
    const contentLength = request.headers.get('content-length');
    if (contentLength) {
        const size = parseInt(contentLength, 10);
        return size <= __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].validation.maxRequestSize;
    }
    return true;
}
function getClientIP(request) {
    // Try to get IP from various headers (in order of preference)
    const headers = [
        'cf-connecting-ip',
        'x-real-ip',
        'x-forwarded-for',
        'x-client-ip',
        'x-cluster-client-ip',
        'forwarded'
    ];
    for (const header of headers){
        const value = request.headers.get(header);
        if (value) {
            // Handle comma-separated IPs (take the first one)
            const ip = value.split(',')[0].trim();
            if (isValidIP(ip)) {
                return ip;
            }
        }
    }
    return 'unknown';
}
function isValidIP(ip) {
    // Basic IP validation (IPv4 and IPv6)
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}
function logSecurityEvent(event) {
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].logging.enableSecurityEvents) {
        console.warn('Security Event:', {
            ...event,
            timestamp: new Date().toISOString(),
            severity: event.type === 'suspicious_activity' ? 'high' : 'medium'
        });
    }
}
}}),
"[project]/src/lib/security/middleware.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "commonSchemas": (()=>commonSchemas),
    "withSecurity": (()=>withSecurity)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/validation.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/utils.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
function withSecurity(handler, options = {}) {
    return async (request, context)=>{
        try {
            const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
            // Enhanced authentication check
            if (options.requireAuth !== false) {
                const authResult = await validateAuthentication(request, supabase, options);
                if (!authResult.success) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: {
                            code: authResult.errorCode,
                            message: authResult.message
                        }
                    }, {
                        status: authResult.status
                    });
                }
                // Add user to context
                if (context) {
                    context.user = authResult.user;
                }
            }
            // Enhanced security checks
            const securityChecks = await performSecurityChecks(request, options);
            if (!securityChecks.success) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: {
                        code: securityChecks.errorCode,
                        message: securityChecks.message,
                        retryAfter: securityChecks.retryAfter
                    }
                }, {
                    status: securityChecks.status,
                    headers: securityChecks.headers || {}
                });
            }
            // Add security check results to context
            if (context) {
                context.securityInfo = securityChecks.info;
            }
            // Input validation
            if (options.validation) {
                const validationResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$validation$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateInput"])(request, options.validation);
                if (!validationResult.success) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                        error: {
                            code: 'VALIDATION_ERROR',
                            message: 'Invalid input data',
                            details: validationResult.errors
                        }
                    }, {
                        status: 400
                    });
                }
                // Add validated data to context to avoid re-reading body
                if (context && validationResult.data) {
                    context.validatedData = validationResult.data;
                }
            }
            // Execute the handler
            const response = await handler(request, context);
            // Add security headers to response
            const secureResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addSecurityHeaders"])(response);
            // Audit logging
            if (options.auditLog && context?.user) {
                await logAuditEvent(request, context.user, secureResponse);
            }
            return secureResponse;
        } catch (error) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleSecureError"])(error);
        }
    };
}
async function performSecurityChecks(request, options) {
    const clientIP = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getClientIP"])(request);
    const userAgent = request.headers.get('user-agent') || '';
    // 1. Request size validation
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateRequestSize"])(request)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logSecurityEvent"])({
            type: 'validation_error',
            ip: clientIP,
            userAgent,
            details: {
                reason: 'request_too_large'
            }
        });
        return {
            success: false,
            errorCode: 'REQUEST_TOO_LARGE',
            message: 'Request size exceeds limit',
            status: 413
        };
    }
    // 2. Suspicious user agent detection
    const suspiciousPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /scanner/i,
        /curl/i,
        /wget/i,
        /python/i,
        /java/i,
        /perl/i
    ];
    if (suspiciousPatterns.some((pattern)=>pattern.test(userAgent))) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logSecurityEvent"])({
            type: 'suspicious_activity',
            ip: clientIP,
            userAgent,
            details: {
                reason: 'suspicious_user_agent'
            }
        });
        return {
            success: false,
            errorCode: 'SUSPICIOUS_REQUEST',
            message: 'Request blocked',
            status: 403
        };
    }
    // 3. Rate limiting
    if (options.rateLimit && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].rateLimit.enabled) {
        const rateLimitResult = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimit"])(`${clientIP}:${request.nextUrl.pathname}`, options.rateLimit.requests, options.rateLimit.window);
        if (!rateLimitResult.success) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logSecurityEvent"])({
                type: 'rate_limit',
                ip: clientIP,
                userAgent,
                details: {
                    endpoint: request.nextUrl.pathname
                }
            });
            return {
                success: false,
                errorCode: 'RATE_LIMIT',
                message: 'Too many requests',
                status: 429,
                retryAfter: rateLimitResult.retryAfter,
                headers: {
                    'Retry-After': rateLimitResult.retryAfter?.toString() || '60'
                }
            };
        }
    }
    // 4. CORS validation
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].cors.enabled) {
        const origin = request.headers.get('origin');
        const method = request.method;
        if (origin && !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].cors.allowedOrigins.includes(origin) && !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].cors.allowedOrigins.includes('*')) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logSecurityEvent"])({
                type: 'suspicious_activity',
                ip: clientIP,
                userAgent,
                details: {
                    reason: 'invalid_origin',
                    origin
                }
            });
            return {
                success: false,
                errorCode: 'CORS_ERROR',
                message: 'Origin not allowed',
                status: 403
            };
        }
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].cors.allowedMethods.includes(method)) {
            return {
                success: false,
                errorCode: 'METHOD_NOT_ALLOWED',
                message: 'Method not allowed',
                status: 405
            };
        }
    }
    // 5. CSRF protection for state-changing operations
    if (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["securityConfig"].csrf.enabled && [
        'POST',
        'PUT',
        'PATCH',
        'DELETE'
    ].includes(request.method)) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["validateCSRFToken"])(request)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["logSecurityEvent"])({
                type: 'suspicious_activity',
                ip: clientIP,
                userAgent,
                details: {
                    reason: 'csrf_token_invalid'
                }
            });
            return {
                success: false,
                errorCode: 'CSRF_ERROR',
                message: 'CSRF token validation failed',
                status: 403
            };
        }
    }
    // 6. Content-Type validation for POST/PUT requests
    if ([
        'POST',
        'PUT',
        'PATCH'
    ].includes(request.method)) {
        const contentType = request.headers.get('content-type');
        if (contentType && !contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {
            return {
                success: false,
                errorCode: 'INVALID_CONTENT_TYPE',
                message: 'Invalid content type',
                status: 415
            };
        }
    }
    return {
        success: true,
        info: {
            clientIP,
            userAgent,
            timestamp: new Date().toISOString()
        }
    };
}
async function validateAuthentication(request, supabase, options) {
    try {
        // Get user from JWT token
        const { data: { user }, error } = await supabase.auth.getUser();
        if (error || !user) {
            return {
                success: false,
                errorCode: 'AUTH_REQUIRED',
                message: 'Authentication required',
                status: 401
            };
        }
        // Check if email verification is required
        if (options.requireEmailVerification && !user.email_confirmed_at) {
            return {
                success: false,
                errorCode: 'EMAIL_NOT_VERIFIED',
                message: 'Email verification required',
                status: 403
            };
        }
        // Check user roles if specified
        if (options.allowedRoles && options.allowedRoles.length > 0) {
            const userRole = user.user_metadata?.role || 'user';
            if (!options.allowedRoles.includes(userRole)) {
                return {
                    success: false,
                    errorCode: 'INSUFFICIENT_PERMISSIONS',
                    message: 'Insufficient permissions',
                    status: 403
                };
            }
        }
        // Validate session freshness (check if token is not too old)
        const { data: { session } } = await supabase.auth.getSession();
        if (session) {
            const tokenAge = Date.now() - (session.expires_at ? session.expires_at * 1000 : 0);
            const maxAge = 24 * 60 * 60 * 1000 // 24 hours
            ;
            if (tokenAge > maxAge) {
                return {
                    success: false,
                    errorCode: 'SESSION_EXPIRED',
                    message: 'Session expired, please login again',
                    status: 401
                };
            }
        }
        return {
            success: true,
            user
        };
    } catch (error) {
        console.error('Authentication validation error:', error);
        return {
            success: false,
            errorCode: 'AUTH_ERROR',
            message: 'Authentication error',
            status: 500
        };
    }
}
async function logAuditEvent(request, user, response) {
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const action = `${request.method} ${request.nextUrl.pathname}`;
        const clientIP = request.ip || request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip');
        await supabase.from('audit_logs').insert([
            {
                user_id: user.id,
                action,
                ip_address: clientIP,
                user_agent: request.headers.get('user-agent'),
                new_values: {
                    status: response.status,
                    method: request.method,
                    path: request.nextUrl.pathname,
                    timestamp: new Date().toISOString()
                }
            }
        ]);
    } catch (error) {
        console.error('Failed to log audit event:', error);
    }
}
const commonSchemas = {
    task: {
        title: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        description: {
            required: false,
            type: 'string',
            maxLength: 1000
        },
        priority: {
            required: false,
            type: 'string',
            enum: [
                'low',
                'medium',
                'high'
            ]
        },
        due_date: {
            required: false,
            type: 'string'
        },
        category: {
            required: false,
            type: 'string',
            maxLength: 100
        },
        estimated_duration: {
            required: false,
            type: 'number',
            min: 1,
            max: 1440
        }
    },
    recipe: {
        title: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        description: {
            required: false,
            type: 'string',
            maxLength: 2000
        },
        ingredients: {
            required: true,
            type: 'array',
            maxItems: 50
        },
        instructions: {
            required: true,
            type: 'array',
            maxItems: 50
        },
        prep_time: {
            required: false,
            type: 'number',
            min: 0,
            max: 1440
        },
        cook_time: {
            required: false,
            type: 'number',
            min: 0,
            max: 1440
        },
        servings: {
            required: false,
            type: 'number',
            min: 1,
            max: 50
        },
        difficulty: {
            required: false,
            type: 'string',
            enum: [
                'easy',
                'medium',
                'hard'
            ]
        }
    },
    shoppingListItem: {
        name: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        quantity: {
            required: false,
            type: 'number',
            min: 0
        },
        unit: {
            required: false,
            type: 'string',
            maxLength: 50
        },
        category: {
            required: false,
            type: 'string',
            maxLength: 100
        },
        priority: {
            required: false,
            type: 'number',
            min: 1,
            max: 5
        }
    },
    transaction: {
        amount: {
            required: true,
            type: 'number'
        },
        description: {
            required: true,
            type: 'string',
            maxLength: 200
        },
        category_id: {
            required: true,
            type: 'string'
        },
        transaction_type: {
            required: true,
            type: 'string',
            enum: [
                'income',
                'expense'
            ]
        },
        date: {
            required: true,
            type: 'string'
        }
    }
};
}}),
"[project]/src/lib/auth/profile-manager.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ensureUserProfile": (()=>ensureUserProfile),
    "getUserProfile": (()=>getUserProfile),
    "updateUserProfile": (()=>updateUserProfile)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
;
async function ensureUserProfile(userId, email, fullName) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        // First, check if profile already exists
        const { data: existingProfile, error: fetchError } = await supabase.from('profiles').select('*').eq('id', userId).single();
        if (existingProfile && !fetchError) {
            return existingProfile;
        }
        // If profile doesn't exist, create it
        const { data: newProfile, error: insertError } = await supabase.from('profiles').insert([
            {
                id: userId,
                email: email,
                full_name: fullName || email.split('@')[0],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            }
        ]).select().single();
        if (insertError) {
            console.error('Error creating user profile:', insertError);
            throw new Error('Failed to create user profile');
        }
        return newProfile;
    } catch (error) {
        console.error('Error in ensureUserProfile:', error);
        throw error;
    }
}
async function getUserProfile(userId) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data: profile, error } = await supabase.from('profiles').select('*').eq('id', userId).single();
        if (error) {
            console.error('Error fetching user profile:', error);
            return null;
        }
        return profile;
    } catch (error) {
        console.error('Error in getUserProfile:', error);
        return null;
    }
}
async function updateUserProfile(userId, updates) {
    const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data: updatedProfile, error } = await supabase.from('profiles').update({
            ...updates,
            updated_at: new Date().toISOString()
        }).eq('id', userId).select().single();
        if (error) {
            console.error('Error updating user profile:', error);
            throw new Error('Failed to update user profile');
        }
        return updatedProfile;
    } catch (error) {
        console.error('Error in updateUserProfile:', error);
        throw error;
    }
}
}}),
"[project]/src/app/api/trips/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/server.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/middleware.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/rate-limit.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/security/error-handler.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$profile$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth/profile-manager.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
const GET = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withSecurity"])(async (request, context)=>{
    try {
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const user = context.user;
        // Ensure user profile exists
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$profile$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureUserProfile"])(user.id, user.email, user.user_metadata?.full_name);
        const { data: trips, error } = await supabase.from('trips').select(`
          id,
          title,
          description,
          destination_country,
          destination_city,
          start_date,
          end_date,
          trip_type,
          status,
          budget_total,
          budget_spent,
          currency,
          traveler_count,
          is_shared,
          cover_image_url,
          notes,
          created_at,
          updated_at
        `).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            trips: trips || []
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addSecurityHeaders"])(response);
    } catch (error) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleSecureError"])(error);
    }
}, {
    requireAuth: true,
    rateLimit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimitConfigs"].api,
    auditLog: true
});
const POST = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$middleware$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["withSecurity"])(async (request, context)=>{
    try {
        const requestData = await request.json();
        const { title, description, destination_country, destination_city, start_date, end_date, trip_type, budget_total, currency, traveler_count, cover_image_url, notes } = requestData;
        // Basic validation
        if (!title || typeof title !== 'string' || title.length > 200) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Invalid title'
                }
            }, {
                status: 400
            });
        }
        if (!destination_country || typeof destination_country !== 'string') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Destination country is required'
                }
            }, {
                status: 400
            });
        }
        if (!start_date || !end_date) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: {
                    code: 'VALIDATION_ERROR',
                    message: 'Start and end dates are required'
                }
            }, {
                status: 400
            });
        }
        const supabase = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$server$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createClient"])();
        const user = context.user;
        // Ensure user profile exists
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2f$profile$2d$manager$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureUserProfile"])(user.id, user.email, user.user_metadata?.full_name);
        const { data: trip, error } = await supabase.from('trips').insert([
            {
                user_id: user.id,
                title: title.trim(),
                description: description || null,
                destination_country: destination_country.trim(),
                destination_city: destination_city?.trim() || null,
                start_date,
                end_date,
                trip_type: trip_type || 'leisure',
                budget_total: budget_total || null,
                currency: currency || 'USD',
                traveler_count: traveler_count || 1,
                cover_image_url: cover_image_url || null,
                notes: notes || null
            }
        ]).select().single();
        if (error) throw error;
        const response = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            trip
        });
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["addSecurityHeaders"])(response);
    } catch (error) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$error$2d$handler$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handleSecureError"])(error);
    }
}, {
    requireAuth: true,
    rateLimit: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$security$2f$rate$2d$limit$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["rateLimitConfigs"].api,
    auditLog: true
});
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__1689eef4._.js.map