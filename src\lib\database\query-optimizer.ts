/**
 * Database Query Optimization Utilities
 * Provides optimized query patterns and performance monitoring
 */

import { createClient } from '@/lib/supabase/server'
import { cacheQuery, cacheManager } from '@/lib/cache/cache-manager'

export interface QueryOptions {
  cache?: boolean
  cacheTTL?: number
  tags?: string[]
  limit?: number
  offset?: number
  orderBy?: { column: string; ascending?: boolean }[]
  select?: string
}

export interface QueryPerformance {
  query: string
  duration: number
  timestamp: number
  cached: boolean
  resultCount: number
}

// Query performance monitoring
class QueryMonitor {
  private queries: QueryPerformance[] = []
  private maxQueries = 1000

  logQuery(performance: QueryPerformance) {
    this.queries.push(performance)
    if (this.queries.length > this.maxQueries) {
      this.queries.shift()
    }
  }

  getSlowQueries(threshold = 1000): QueryPerformance[] {
    return this.queries.filter(q => q.duration > threshold)
  }

  getAverageQueryTime(): number {
    if (this.queries.length === 0) return 0
    const total = this.queries.reduce((sum, q) => sum + q.duration, 0)
    return total / this.queries.length
  }

  getCacheHitRate(): number {
    if (this.queries.length === 0) return 0
    const cached = this.queries.filter(q => q.cached).length
    return (cached / this.queries.length) * 100
  }

  getStats() {
    return {
      totalQueries: this.queries.length,
      averageTime: this.getAverageQueryTime(),
      cacheHitRate: this.getCacheHitRate(),
      slowQueries: this.getSlowQueries().length
    }
  }
}

export const queryMonitor = new QueryMonitor()

// Optimized query builder
export class OptimizedQueryBuilder {
  private supabase: any
  private tableName: string
  private selectFields: string = '*'
  private filters: Array<{ column: string; operator: string; value: any }> = []
  private orderFields: Array<{ column: string; ascending: boolean }> = []
  private limitValue?: number
  private offsetValue?: number

  constructor(tableName: string) {
    this.tableName = tableName
  }

  async init() {
    this.supabase = await createClient()
    return this
  }

  select(fields: string) {
    this.selectFields = fields
    return this
  }

  eq(column: string, value: any) {
    this.filters.push({ column, operator: 'eq', value })
    return this
  }

  neq(column: string, value: any) {
    this.filters.push({ column, operator: 'neq', value })
    return this
  }

  gt(column: string, value: any) {
    this.filters.push({ column, operator: 'gt', value })
    return this
  }

  gte(column: string, value: any) {
    this.filters.push({ column, operator: 'gte', value })
    return this
  }

  lt(column: string, value: any) {
    this.filters.push({ column, operator: 'lt', value })
    return this
  }

  lte(column: string, value: any) {
    this.filters.push({ column, operator: 'lte', value })
    return this
  }

  like(column: string, value: string) {
    this.filters.push({ column, operator: 'like', value })
    return this
  }

  in(column: string, values: any[]) {
    this.filters.push({ column, operator: 'in', value: values })
    return this
  }

  order(column: string, ascending = true) {
    this.orderFields.push({ column, ascending })
    return this
  }

  limit(count: number) {
    this.limitValue = count
    return this
  }

  offset(count: number) {
    this.offsetValue = count
    return this
  }

  async execute<T>(options: QueryOptions = {}): Promise<{ data: T[] | null; error: any; performance: QueryPerformance }> {
    const startTime = Date.now()
    const queryKey = this.buildCacheKey()
    
    // Check cache first if enabled
    if (options.cache !== false) {
      const cached = await cacheManager.get<{ data: T[]; error: any }>(queryKey, 'database')
      if (cached) {
        const performance: QueryPerformance = {
          query: this.buildQueryString(),
          duration: Date.now() - startTime,
          timestamp: Date.now(),
          cached: true,
          resultCount: cached.data?.length || 0
        }
        queryMonitor.logQuery(performance)
        return { ...cached, performance }
      }
    }

    // Build and execute query
    let query = this.supabase.from(this.tableName).select(this.selectFields)

    // Apply filters
    for (const filter of this.filters) {
      query = query[filter.operator](filter.column, filter.value)
    }

    // Apply ordering
    for (const order of this.orderFields) {
      query = query.order(order.column, { ascending: order.ascending })
    }

    // Apply pagination
    if (this.limitValue) {
      query = query.limit(this.limitValue)
    }
    if (this.offsetValue) {
      query = query.range(this.offsetValue, this.offsetValue + (this.limitValue || 1000) - 1)
    }

    const result = await query
    const endTime = Date.now()

    const performance: QueryPerformance = {
      query: this.buildQueryString(),
      duration: endTime - startTime,
      timestamp: endTime,
      cached: false,
      resultCount: result.data?.length || 0
    }

    queryMonitor.logQuery(performance)

    // Cache result if successful and caching is enabled
    if (options.cache !== false && !result.error) {
      await cacheManager.set(queryKey, { data: result.data, error: result.error }, {
        ttl: options.cacheTTL || 300, // 5 minutes default
        tags: ['database', this.tableName, ...(options.tags || [])],
        namespace: 'database'
      })
    }

    return { ...result, performance }
  }

  private buildCacheKey(): string {
    const parts = [
      this.tableName,
      this.selectFields,
      JSON.stringify(this.filters),
      JSON.stringify(this.orderFields),
      this.limitValue?.toString() || '',
      this.offsetValue?.toString() || ''
    ]
    return parts.join(':')
  }

  private buildQueryString(): string {
    let query = `SELECT ${this.selectFields} FROM ${this.tableName}`
    
    if (this.filters.length > 0) {
      const whereClause = this.filters.map(f => `${f.column} ${f.operator} ${JSON.stringify(f.value)}`).join(' AND ')
      query += ` WHERE ${whereClause}`
    }
    
    if (this.orderFields.length > 0) {
      const orderClause = this.orderFields.map(o => `${o.column} ${o.ascending ? 'ASC' : 'DESC'}`).join(', ')
      query += ` ORDER BY ${orderClause}`
    }
    
    if (this.limitValue) {
      query += ` LIMIT ${this.limitValue}`
    }
    
    if (this.offsetValue) {
      query += ` OFFSET ${this.offsetValue}`
    }
    
    return query
  }
}

// Optimized query functions for common patterns
export async function getUserData<T>(
  userId: string,
  tableName: string,
  options: QueryOptions = {}
): Promise<{ data: T[] | null; error: any }> {
  const builder = new OptimizedQueryBuilder(tableName)
  await builder.init()
  
  const result = await builder
    .select(options.select || '*')
    .eq('user_id', userId)
    .limit(options.limit || 100)
    .execute<T>({
      cache: true,
      cacheTTL: 300,
      tags: [`user:${userId}`, tableName],
      ...options
    })

  return { data: result.data, error: result.error }
}

export async function getRecentItems<T>(
  userId: string,
  tableName: string,
  dateColumn = 'created_at',
  limit = 10,
  options: QueryOptions = {}
): Promise<{ data: T[] | null; error: any }> {
  const builder = new OptimizedQueryBuilder(tableName)
  await builder.init()
  
  const result = await builder
    .select(options.select || '*')
    .eq('user_id', userId)
    .order(dateColumn, false)
    .limit(limit)
    .execute<T>({
      cache: true,
      cacheTTL: 60, // 1 minute for recent items
      tags: [`user:${userId}`, tableName, 'recent'],
      ...options
    })

  return { data: result.data, error: result.error }
}

export async function searchItems<T>(
  userId: string,
  tableName: string,
  searchColumn: string,
  searchTerm: string,
  options: QueryOptions = {}
): Promise<{ data: T[] | null; error: any }> {
  const builder = new OptimizedQueryBuilder(tableName)
  await builder.init()
  
  const result = await builder
    .select(options.select || '*')
    .eq('user_id', userId)
    .like(searchColumn, `%${searchTerm}%`)
    .limit(options.limit || 50)
    .execute<T>({
      cache: false, // Don't cache search results
      ...options
    })

  return { data: result.data, error: result.error }
}

// Batch operations for better performance
export async function batchInsert<T>(
  tableName: string,
  items: Partial<T>[],
  batchSize = 100
): Promise<{ data: T[] | null; error: any }> {
  const supabase = await createClient()
  const results: T[] = []
  const errors: any[] = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    const { data, error } = await supabase
      .from(tableName)
      .insert(batch)
      .select()

    if (error) {
      errors.push(error)
    } else if (data) {
      results.push(...data)
    }
  }

  // Invalidate cache for this table
  await cacheManager.invalidateByTag(tableName)

  return {
    data: results.length > 0 ? results : null,
    error: errors.length > 0 ? errors : null
  }
}

export async function batchUpdate<T>(
  tableName: string,
  updates: Array<{ id: string; data: Partial<T> }>,
  batchSize = 50
): Promise<{ data: T[] | null; error: any }> {
  const supabase = await createClient()
  const results: T[] = []
  const errors: any[] = []

  for (let i = 0; i < updates.length; i += batchSize) {
    const batch = updates.slice(i, i + batchSize)
    
    for (const update of batch) {
      const { data, error } = await supabase
        .from(tableName)
        .update(update.data)
        .eq('id', update.id)
        .select()
        .single()

      if (error) {
        errors.push(error)
      } else if (data) {
        results.push(data)
      }
    }
  }

  // Invalidate cache for this table
  await cacheManager.invalidateByTag(tableName)

  return {
    data: results.length > 0 ? results : null,
    error: errors.length > 0 ? errors : null
  }
}

// Query optimization recommendations
export function analyzeQueryPerformance(): {
  recommendations: string[]
  stats: any
} {
  const stats = queryMonitor.getStats()
  const slowQueries = queryMonitor.getSlowQueries()
  const recommendations: string[] = []

  if (stats.averageTime > 500) {
    recommendations.push('Consider adding database indexes for frequently queried columns')
  }

  if (stats.cacheHitRate < 50) {
    recommendations.push('Increase cache TTL for frequently accessed data')
  }

  if (slowQueries.length > 10) {
    recommendations.push('Review and optimize slow queries')
  }

  return { recommendations, stats }
}
