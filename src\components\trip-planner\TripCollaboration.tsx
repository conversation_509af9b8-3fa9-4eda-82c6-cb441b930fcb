'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { 
  ShareIcon, 
  UsersIcon, 
  PlusIcon, 
  CopyIcon,
  MailIcon,
  TrashIcon,
  SettingsIcon,
  ActivityIcon,
  LinkIcon,
  CheckIcon
} from 'lucide-react'
import { tripCollaborationService, type TripCollaborator, type TripActivity } from '@/lib/services/trip-collaboration-service'
import toast from 'react-hot-toast'

interface TripCollaborationProps {
  tripId: string
  isOwner: boolean
  className?: string
}

export default function TripCollaboration({ tripId, isOwner, className = '' }: TripCollaborationProps) {
  const [collaborators, setCollaborators] = useState<TripCollaborator[]>([])
  const [activities, setActivities] = useState<TripActivity[]>([])
  const [shareCode, setShareCode] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [showInviteForm, setShowInviteForm] = useState(false)
  const [showActivity, setShowActivity] = useState(false)
  const [inviteEmail, setInviteEmail] = useState('')
  const [invitePermission, setInvitePermission] = useState<'view' | 'edit' | 'admin'>('view')

  useEffect(() => {
    loadCollaborators()
    loadActivity()
  }, [tripId])

  const loadCollaborators = async () => {
    try {
      const data = await tripCollaborationService.getCollaborators(tripId)
      setCollaborators(data)
    } catch (error) {
      console.error('Failed to load collaborators:', error)
    }
  }

  const loadActivity = async () => {
    try {
      const data = await tripCollaborationService.getTripActivity(tripId, 20)
      setActivities(data)
    } catch (error) {
      console.error('Failed to load activity:', error)
    }
  }

  const handleShareTrip = async () => {
    if (!isOwner) return

    setIsLoading(true)
    try {
      const result = await tripCollaborationService.shareTrip(tripId, {
        permission_level: 'view',
        share_via_link: true
      })
      
      if (result.share_code) {
        setShareCode(result.share_code)
        toast.success('Trip shared! Share code generated.')
      }
    } catch (error) {
      console.error('Failed to share trip:', error)
      toast.error('Failed to share trip')
    } finally {
      setIsLoading(false)
    }
  }

  const handleInviteCollaborator = async () => {
    if (!inviteEmail.trim()) {
      toast.error('Please enter an email address')
      return
    }

    setIsLoading(true)
    try {
      await tripCollaborationService.inviteCollaborator(tripId, inviteEmail, invitePermission)
      toast.success(`Invitation sent to ${inviteEmail}`)
      setInviteEmail('')
      setShowInviteForm(false)
      loadCollaborators()
    } catch (error) {
      console.error('Failed to invite collaborator:', error)
      toast.error('Failed to send invitation')
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveCollaborator = async (collaboratorId: string) => {
    if (!confirm('Are you sure you want to remove this collaborator?')) return

    try {
      await tripCollaborationService.removeCollaborator(collaboratorId)
      toast.success('Collaborator removed')
      loadCollaborators()
      loadActivity()
    } catch (error) {
      console.error('Failed to remove collaborator:', error)
      toast.error('Failed to remove collaborator')
    }
  }

  const copyShareLink = () => {
    if (shareCode) {
      const shareUrl = `${window.location.origin}/trips/join/${shareCode}`
      navigator.clipboard.writeText(shareUrl)
      toast.success('Share link copied to clipboard!')
    }
  }

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'edit': return 'bg-blue-100 text-blue-800'
      case 'view': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'declined': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatActivityTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `${diffDays}d ago`
    } else if (diffHours > 0) {
      return `${diffHours}h ago`
    } else {
      return 'Just now'
    }
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <UsersIcon className="h-5 w-5 text-blue-600" />
            <h3 className="font-semibold text-gray-900">Collaboration</h3>
            <Badge variant="secondary">{collaborators.length} collaborators</Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowActivity(!showActivity)}
            >
              <ActivityIcon className="h-4 w-4 mr-1" />
              Activity
            </Button>
            
            {isOwner && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowInviteForm(!showInviteForm)}
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Invite
                </Button>
                
                <Button
                  onClick={handleShareTrip}
                  disabled={isLoading}
                  size="sm"
                >
                  <ShareIcon className="h-4 w-4 mr-2" />
                  Share Trip
                </Button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Share Link */}
      {shareCode && (
        <div className="p-4 bg-blue-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-blue-900 mb-1">Share Link</h4>
              <p className="text-sm text-blue-700">Anyone with this link can join the trip</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={copyShareLink}
            >
              <CopyIcon className="h-4 w-4 mr-1" />
              Copy Link
            </Button>
          </div>
          <div className="mt-2 p-2 bg-white rounded border text-sm font-mono text-gray-600">
            {`${window.location.origin}/trips/join/${shareCode}`}
          </div>
        </div>
      )}

      {/* Invite Form */}
      {showInviteForm && (
        <div className="p-4 bg-gray-50 border-b border-gray-200">
          <h4 className="font-medium text-gray-900 mb-3">Invite Collaborator</h4>
          <div className="space-y-3">
            <div>
              <Input
                type="email"
                placeholder="Enter email address"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>
            <div>
              <select
                value={invitePermission}
                onChange={(e) => setInvitePermission(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="view">View Only</option>
                <option value="edit">Can Edit</option>
                <option value="admin">Admin</option>
              </select>
            </div>
            <div className="flex space-x-2">
              <Button
                onClick={handleInviteCollaborator}
                disabled={isLoading}
                size="sm"
              >
                <MailIcon className="h-4 w-4 mr-1" />
                Send Invitation
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowInviteForm(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Collaborators List */}
      <div className="p-4">
        <h4 className="font-medium text-gray-900 mb-3">Collaborators</h4>
        <div className="space-y-3">
          {collaborators.map((collaborator) => (
            <div key={collaborator.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  {collaborator.user?.full_name?.charAt(0) || collaborator.user?.email?.charAt(0) || '?'}
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {collaborator.user?.full_name || collaborator.user?.email || 'Unknown User'}
                  </p>
                  <p className="text-sm text-gray-600">{collaborator.user?.email}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Badge className={getPermissionColor(collaborator.permission_level)}>
                  {collaborator.permission_level}
                </Badge>
                <Badge className={getStatusColor(collaborator.status)}>
                  {collaborator.status}
                </Badge>
                
                {isOwner && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveCollaborator(collaborator.id)}
                  >
                    <TrashIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          ))}
          
          {collaborators.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <UsersIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No collaborators yet</p>
              {isOwner && (
                <p className="text-sm mt-1">Invite people to collaborate on this trip</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Activity Log */}
      {showActivity && (
        <div className="border-t border-gray-200 p-4">
          <h4 className="font-medium text-gray-900 mb-3">Recent Activity</h4>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {activities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3 text-sm">
                <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center text-xs">
                  {activity.user?.full_name?.charAt(0) || '?'}
                </div>
                <div className="flex-1">
                  <p className="text-gray-900">
                    <span className="font-medium">
                      {activity.user?.full_name || activity.user?.email || 'Someone'}
                    </span>{' '}
                    {activity.description || activity.action_type}
                  </p>
                  <p className="text-gray-500 text-xs">{formatActivityTime(activity.created_at)}</p>
                </div>
              </div>
            ))}
            
            {activities.length === 0 && (
              <p className="text-gray-500 text-center py-4">No recent activity</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
