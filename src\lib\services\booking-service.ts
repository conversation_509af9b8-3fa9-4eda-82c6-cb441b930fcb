export interface BookingProvider {
  id: string
  name: string
  type: 'accommodation' | 'flight' | 'car_rental' | 'activity' | 'restaurant'
  logo_url?: string
  api_endpoint?: string
  supported_countries: string[]
  features: string[]
}

export interface BookingSearchParams {
  type: 'accommodation' | 'flight' | 'car_rental' | 'activity' | 'restaurant'
  location: {
    latitude: number
    longitude: number
    city?: string
    country?: string
  }
  dates: {
    check_in: Date
    check_out: Date
  }
  guests?: {
    adults: number
    children: number
    rooms?: number
  }
  budget?: {
    min: number
    max: number
    currency: string
  }
  preferences?: {
    rating_min?: number
    amenities?: string[]
    categories?: string[]
  }
}

export interface BookingOption {
  id: string
  provider_id: string
  type: string
  name: string
  description?: string
  location: {
    address: string
    latitude: number
    longitude: number
  }
  price: {
    amount: number
    currency: string
    per_night?: boolean
    total?: number
  }
  rating?: {
    score: number
    reviews_count: number
  }
  images: string[]
  amenities: string[]
  availability: {
    available: boolean
    last_updated: Date
  }
  booking_url: string
  cancellation_policy?: string
  provider_data?: any
}

export interface BookingReservation {
  id: string
  trip_id: string
  user_id: string
  provider_id: string
  booking_reference: string
  type: string
  name: string
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  dates: {
    check_in: Date
    check_out: Date
  }
  guests: {
    adults: number
    children: number
  }
  price: {
    amount: number
    currency: string
    total: number
  }
  location: {
    address: string
    latitude: number
    longitude: number
  }
  confirmation_details?: any
  created_at: Date
  updated_at: Date
}

class BookingService {
  private providers: BookingProvider[] = [
    {
      id: 'booking_com',
      name: 'Booking.com',
      type: 'accommodation',
      logo_url: '/logos/booking-com.png',
      supported_countries: ['*'], // Global
      features: ['hotels', 'apartments', 'hostels', 'resorts']
    },
    {
      id: 'airbnb',
      name: 'Airbnb',
      type: 'accommodation',
      logo_url: '/logos/airbnb.png',
      supported_countries: ['*'], // Global
      features: ['apartments', 'houses', 'unique_stays']
    },
    {
      id: 'expedia',
      name: 'Expedia',
      type: 'flight',
      logo_url: '/logos/expedia.png',
      supported_countries: ['*'], // Global
      features: ['flights', 'hotels', 'packages']
    },
    {
      id: 'skyscanner',
      name: 'Skyscanner',
      type: 'flight',
      logo_url: '/logos/skyscanner.png',
      supported_countries: ['*'], // Global
      features: ['flights', 'price_comparison']
    },
    {
      id: 'rentalcars',
      name: 'RentalCars.com',
      type: 'car_rental',
      logo_url: '/logos/rentalcars.png',
      supported_countries: ['*'], // Global
      features: ['car_rental', 'price_comparison']
    },
    {
      id: 'viator',
      name: 'Viator',
      type: 'activity',
      logo_url: '/logos/viator.png',
      supported_countries: ['*'], // Global
      features: ['tours', 'activities', 'experiences']
    },
    {
      id: 'opentable',
      name: 'OpenTable',
      type: 'restaurant',
      logo_url: '/logos/opentable.png',
      supported_countries: ['US', 'CA', 'UK', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'JP'],
      features: ['restaurant_reservations', 'reviews']
    }
  ]

  getProviders(type?: string, country?: string): BookingProvider[] {
    let filtered = this.providers

    if (type) {
      filtered = filtered.filter(p => p.type === type)
    }

    if (country) {
      filtered = filtered.filter(p => 
        p.supported_countries.includes('*') || 
        p.supported_countries.includes(country)
      )
    }

    return filtered
  }

  async searchBookingOptions(params: BookingSearchParams): Promise<BookingOption[]> {
    // This is a mock implementation - in production, this would integrate with actual booking APIs
    const providers = this.getProviders(params.type, params.location.country)
    const mockResults: BookingOption[] = []

    // Generate mock results for demonstration
    for (const provider of providers.slice(0, 3)) {
      for (let i = 0; i < 5; i++) {
        mockResults.push({
          id: `${provider.id}_${i}`,
          provider_id: provider.id,
          type: params.type,
          name: this.generateMockName(params.type, i),
          description: this.generateMockDescription(params.type),
          location: {
            address: `${params.location.city}, ${params.location.country}`,
            latitude: params.location.latitude + (Math.random() - 0.5) * 0.01,
            longitude: params.location.longitude + (Math.random() - 0.5) * 0.01
          },
          price: {
            amount: Math.floor(Math.random() * 200) + 50,
            currency: params.budget?.currency || 'USD',
            per_night: params.type === 'accommodation',
            total: Math.floor(Math.random() * 1000) + 200
          },
          rating: {
            score: Math.round((Math.random() * 2 + 3) * 10) / 10,
            reviews_count: Math.floor(Math.random() * 1000) + 50
          },
          images: [
            `/mock-images/${params.type}_${i}_1.jpg`,
            `/mock-images/${params.type}_${i}_2.jpg`
          ],
          amenities: this.generateMockAmenities(params.type),
          availability: {
            available: Math.random() > 0.2,
            last_updated: new Date()
          },
          booking_url: `https://${provider.id}.com/book/${provider.id}_${i}`,
          cancellation_policy: 'Free cancellation up to 24 hours before check-in'
        })
      }
    }

    // Sort by price or rating
    return mockResults.sort((a, b) => a.price.amount - b.price.amount)
  }

  generateBookingUrl(option: BookingOption, params: BookingSearchParams): string {
    const baseUrl = option.booking_url
    const searchParams = new URLSearchParams({
      checkin: params.dates.check_in.toISOString().split('T')[0],
      checkout: params.dates.check_out.toISOString().split('T')[0],
      adults: params.guests?.adults.toString() || '2',
      children: params.guests?.children.toString() || '0'
    })

    return `${baseUrl}?${searchParams.toString()}`
  }

  async createReservation(
    tripId: string,
    option: BookingOption,
    params: BookingSearchParams
  ): Promise<BookingReservation> {
    // Mock reservation creation - in production, this would integrate with booking APIs
    const reservation: BookingReservation = {
      id: `res_${Date.now()}`,
      trip_id: tripId,
      user_id: 'current_user_id', // Would get from auth
      provider_id: option.provider_id,
      booking_reference: `BK${Math.random().toString(36).substring(2, 8).toUpperCase()}`,
      type: option.type,
      name: option.name,
      status: 'pending',
      dates: params.dates,
      guests: params.guests || { adults: 2, children: 0 },
      price: {
        amount: option.price.amount,
        currency: option.price.currency,
        total: option.price.total || option.price.amount
      },
      location: option.location,
      created_at: new Date(),
      updated_at: new Date()
    }

    // In production, save to database
    console.log('Created reservation:', reservation)
    
    return reservation
  }

  private generateMockName(type: string, index: number): string {
    const names = {
      accommodation: [
        'Grand Plaza Hotel', 'Cozy Downtown Apartment', 'Luxury Resort & Spa',
        'Boutique City Hotel', 'Modern Loft with View'
      ],
      flight: [
        'Economy Flight', 'Business Class', 'Premium Economy',
        'First Class', 'Budget Airline'
      ],
      car_rental: [
        'Economy Car', 'Compact SUV', 'Luxury Sedan',
        'Full-size SUV', 'Convertible'
      ],
      activity: [
        'City Walking Tour', 'Museum Visit', 'Food Tasting Tour',
        'Adventure Experience', 'Cultural Workshop'
      ],
      restaurant: [
        'Fine Dining Restaurant', 'Local Bistro', 'Rooftop Bar & Grill',
        'Traditional Cuisine', 'Modern Fusion'
      ]
    }

    return names[type]?.[index] || `${type} Option ${index + 1}`
  }

  private generateMockDescription(type: string): string {
    const descriptions = {
      accommodation: 'Comfortable accommodation with modern amenities and great location.',
      flight: 'Direct flight with complimentary refreshments and entertainment.',
      car_rental: 'Well-maintained vehicle with full insurance coverage.',
      activity: 'Exciting experience with knowledgeable local guides.',
      restaurant: 'Authentic cuisine in a welcoming atmosphere.'
    }

    return descriptions[type] || 'Great option for your trip.'
  }

  private generateMockAmenities(type: string): string[] {
    const amenities = {
      accommodation: ['WiFi', 'Air Conditioning', 'Pool', 'Gym', 'Restaurant', 'Parking'],
      flight: ['In-flight Entertainment', 'Meals', 'WiFi', 'Extra Legroom'],
      car_rental: ['GPS', 'Air Conditioning', 'Bluetooth', 'USB Charging'],
      activity: ['Guide Included', 'Transportation', 'Equipment Provided', 'Small Group'],
      restaurant: ['Outdoor Seating', 'Wine List', 'Vegetarian Options', 'Reservations']
    }

    const available = amenities[type] || []
    return available.slice(0, Math.floor(Math.random() * 4) + 2)
  }
}

export const bookingService = new BookingService()

// Booking integration utilities
export const BookingIntegration = {
  // Generate affiliate links (for future monetization)
  generateAffiliateLink: (provider: string, originalUrl: string, affiliateId?: string): string => {
    if (!affiliateId) return originalUrl

    const affiliateParams = {
      booking_com: `aid=${affiliateId}`,
      airbnb: `ref=${affiliateId}`,
      expedia: `affiliate=${affiliateId}`,
      skyscanner: `partner=${affiliateId}`
    }

    const param = affiliateParams[provider]
    if (!param) return originalUrl

    const separator = originalUrl.includes('?') ? '&' : '?'
    return `${originalUrl}${separator}${param}`
  },

  // Track booking clicks for analytics
  trackBookingClick: (provider: string, optionId: string, tripId: string): void => {
    // In production, send to analytics service
    console.log('Booking click tracked:', { provider, optionId, tripId })
  },

  // Get provider-specific booking instructions
  getBookingInstructions: (provider: string): string => {
    const instructions = {
      booking_com: 'Click to view available rooms and make a reservation on Booking.com',
      airbnb: 'View this property and book directly through Airbnb',
      expedia: 'Compare prices and book flights through Expedia',
      skyscanner: 'Find the best flight deals on Skyscanner',
      rentalcars: 'Compare car rental prices and book through RentalCars.com',
      viator: 'Book tours and activities through Viator',
      opentable: 'Make a restaurant reservation through OpenTable'
    }

    return instructions[provider] || 'Click to view booking options'
  }
}
