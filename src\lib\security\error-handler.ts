import { NextResponse, NextRequest } from 'next/server'
import { securityConfig } from './config'
import crypto from 'crypto'

export interface SecureError {
  code: string
  message: string
  statusCode: number
  severity?: 'low' | 'medium' | 'high' | 'critical'
  category?: 'auth' | 'validation' | 'rate_limit' | 'database' | 'security' | 'system'
}

export interface ErrorContext {
  userId?: string
  ip?: string
  userAgent?: string
  endpoint?: string
  method?: string
  timestamp?: string
  requestId?: string
  sessionId?: string
}

// Enhanced secure error mappings with severity and category
const ERROR_MAPPINGS: Record<string, SecureError> = {
  // Authentication errors
  'PGRST301': {
    code: 'AUTH_REQUIRED',
    message: 'Authentication required',
    statusCode: 401,
    severity: 'medium',
    category: 'auth'
  },
  'PGRST302': {
    code: 'FORBIDDEN',
    message: 'Access denied',
    statusCode: 403,
    severity: 'high',
    category: 'auth'
  },
  'AUTH_FAILED': {
    code: 'AUTH_FAILED',
    message: 'Authentication failed',
    statusCode: 401,
    severity: 'medium',
    category: 'auth'
  },
  'SESSION_EXPIRED': {
    code: 'SESSION_EXPIRED',
    message: 'Session expired',
    statusCode: 401,
    severity: 'low',
    category: 'auth'
  },
  'EMAIL_NOT_VERIFIED': {
    code: 'EMAIL_NOT_VERIFIED',
    message: 'Email verification required',
    statusCode: 403,
    severity: 'medium',
    category: 'auth'
  },
  'ACCOUNT_SUSPENDED': {
    code: 'ACCOUNT_SUSPENDED',
    message: 'Account suspended',
    statusCode: 403,
    severity: 'high',
    category: 'auth'
  },

  // Database constraint errors
  '23503': {
    code: 'INVALID_REFERENCE',
    message: 'Invalid data reference',
    statusCode: 400,
    severity: 'low',
    category: 'validation'
  },
  '23505': {
    code: 'DUPLICATE_ENTRY',
    message: 'Duplicate entry not allowed',
    statusCode: 409,
    severity: 'low',
    category: 'validation'
  },
  '23514': {
    code: 'VALIDATION_ERROR',
    message: 'Data validation failed',
    statusCode: 400,
    severity: 'low',
    category: 'validation'
  },

  // Rate limiting
  'RATE_LIMIT': {
    code: 'RATE_LIMIT',
    message: 'Too many requests',
    statusCode: 429,
    severity: 'medium',
    category: 'rate_limit'
  },

  // Security errors
  'CSRF_ERROR': {
    code: 'CSRF_ERROR',
    message: 'CSRF token validation failed',
    statusCode: 403,
    severity: 'high',
    category: 'security'
  },
  'SUSPICIOUS_REQUEST': {
    code: 'SUSPICIOUS_REQUEST',
    message: 'Request blocked',
    statusCode: 403,
    severity: 'high',
    category: 'security'
  },
  'SQL_INJECTION_ATTEMPT': {
    code: 'SECURITY_VIOLATION',
    message: 'Request blocked',
    statusCode: 403,
    severity: 'critical',
    category: 'security'
  },
  'XSS_ATTEMPT': {
    code: 'SECURITY_VIOLATION',
    message: 'Request blocked',
    statusCode: 403,
    severity: 'critical',
    category: 'security'
  },

  // Validation errors
  'VALIDATION_ERROR': {
    code: 'VALIDATION_ERROR',
    message: 'Invalid input data',
    statusCode: 400,
    severity: 'low',
    category: 'validation'
  },
  'REQUEST_TOO_LARGE': {
    code: 'REQUEST_TOO_LARGE',
    message: 'Request size exceeds limit',
    statusCode: 413,
    severity: 'medium',
    category: 'validation'
  },
  'INVALID_CONTENT_TYPE': {
    code: 'INVALID_CONTENT_TYPE',
    message: 'Invalid content type',
    statusCode: 415,
    severity: 'low',
    category: 'validation'
  },

  // System errors
  'INTERNAL_ERROR': {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred',
    statusCode: 500,
    severity: 'high',
    category: 'system'
  },
  'SERVICE_UNAVAILABLE': {
    code: 'SERVICE_UNAVAILABLE',
    message: 'Service temporarily unavailable',
    statusCode: 503,
    severity: 'high',
    category: 'system'
  },
  'DATABASE_ERROR': {
    code: 'DATABASE_ERROR',
    message: 'Database operation failed',
    statusCode: 500,
    severity: 'high',
    category: 'database'
  },
}

export function handleSecureError(
  error: any,
  customMessage?: string,
  context?: ErrorContext
): NextResponse {
  const errorId = crypto.randomUUID()
  const timestamp = new Date().toISOString()

  // Determine the error type
  let secureError: SecureError = determineErrorType(error)

  // Override message if provided
  if (customMessage) {
    secureError = { ...secureError, message: customMessage }
  }

  // Enhanced error logging with context
  const errorLog = {
    errorId,
    timestamp,
    error: {
      name: error.name,
      message: error.message,
      code: error.code,
      stack: securityConfig.logging.logLevel === 'debug' ? error.stack : undefined,
      details: error.details,
    },
    secureError,
    context: {
      userId: context?.userId,
      ip: context?.ip,
      userAgent: context?.userAgent,
      endpoint: context?.endpoint,
      method: context?.method,
      requestId: context?.requestId,
      sessionId: context?.sessionId,
    },
    severity: secureError.severity,
    category: secureError.category,
  }

  // Log based on severity and configuration
  logError(errorLog)

  // Send alerts for critical errors
  if (secureError.severity === 'critical') {
    sendCriticalErrorAlert(errorLog)
  }

  // Prepare response
  const responseBody: any = {
    error: {
      code: secureError.code,
      message: secureError.message,
      ...(securityConfig.logging.logLevel === 'debug' && { errorId })
    }
  }

  // Add additional debug info in development
  if (securityConfig.logging.logLevel === 'debug' && process.env.NODE_ENV === 'development') {
    responseBody.debug = {
      originalError: error.message,
      timestamp,
      category: secureError.category,
      severity: secureError.severity
    }
  }

  const response = NextResponse.json(responseBody, { status: secureError.statusCode })

  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')

  return response
}

function determineErrorType(error: any): SecureError {
  // Check for exact code match
  if (error.code && ERROR_MAPPINGS[error.code]) {
    return ERROR_MAPPINGS[error.code]
  }

  // Check for error name match
  if (error.name && ERROR_MAPPINGS[error.name]) {
    return ERROR_MAPPINGS[error.name]
  }

  // Pattern matching for common error types
  const message = error.message?.toLowerCase() || ''

  if (message.includes('rate limit') || message.includes('too many requests')) {
    return ERROR_MAPPINGS['RATE_LIMIT']
  }

  if (message.includes('validation') || message.includes('invalid')) {
    return ERROR_MAPPINGS['VALIDATION_ERROR']
  }

  if (message.includes('auth') || message.includes('unauthorized')) {
    return ERROR_MAPPINGS['AUTH_REQUIRED']
  }

  if (message.includes('forbidden') || message.includes('access denied')) {
    return ERROR_MAPPINGS['PGRST302']
  }

  if (message.includes('duplicate') || message.includes('already exists')) {
    return ERROR_MAPPINGS['23505']
  }

  // Security pattern detection
  if (detectSQLInjection(message)) {
    return ERROR_MAPPINGS['SQL_INJECTION_ATTEMPT']
  }

  if (detectXSSAttempt(message)) {
    return ERROR_MAPPINGS['XSS_ATTEMPT']
  }

  // Database errors
  if (message.includes('database') || message.includes('connection') || error.code?.startsWith('23')) {
    return ERROR_MAPPINGS['DATABASE_ERROR']
  }

  // Default to internal error
  return ERROR_MAPPINGS['INTERNAL_ERROR']
}

function detectSQLInjection(message: string): boolean {
  const sqlPatterns = [
    /union.*select/i,
    /drop.*table/i,
    /insert.*into/i,
    /delete.*from/i,
    /update.*set/i,
    /exec.*xp_/i,
    /information_schema/i,
    /sysobjects/i,
    /syscolumns/i
  ]

  return sqlPatterns.some(pattern => pattern.test(message))
}

function detectXSSAttempt(message: string): boolean {
  const xssPatterns = [
    /<script/i,
    /javascript:/i,
    /vbscript:/i,
    /onload=/i,
    /onerror=/i,
    /onclick=/i,
    /eval\(/i,
    /expression\(/i
  ]

  return xssPatterns.some(pattern => pattern.test(message))
}

function logError(errorLog: any): void {
  const { severity, category } = errorLog.secureError

  // Determine log level based on severity
  switch (severity) {
    case 'critical':
      console.error('🚨 CRITICAL ERROR:', errorLog)
      break
    case 'high':
      console.error('❌ HIGH SEVERITY ERROR:', errorLog)
      break
    case 'medium':
      console.warn('⚠️ MEDIUM SEVERITY ERROR:', errorLog)
      break
    case 'low':
      if (securityConfig.logging.logLevel === 'debug') {
        console.info('ℹ️ LOW SEVERITY ERROR:', errorLog)
      }
      break
    default:
      console.error('ERROR:', errorLog)
  }

  // Log to external monitoring service if configured
  if (process.env.SENTRY_DSN && (severity === 'critical' || severity === 'high')) {
    // In a real implementation, you would send to Sentry here
    console.log('Would send to Sentry:', errorLog.errorId)
  }

  // Store in audit log for security events
  if (category === 'security' || category === 'auth') {
    storeSecurityAuditLog(errorLog)
  }
}

function sendCriticalErrorAlert(errorLog: any): void {
  // In production, this would send alerts via email, Slack, PagerDuty, etc.
  console.error('🚨 CRITICAL ERROR ALERT 🚨', {
    errorId: errorLog.errorId,
    message: errorLog.secureError.message,
    category: errorLog.secureError.category,
    timestamp: errorLog.timestamp,
    context: errorLog.context
  })

  // Example: Send to monitoring service
  if (process.env.WEBHOOK_URL) {
    // In a real implementation, you would send a webhook here
    console.log('Would send webhook alert for:', errorLog.errorId)
  }
}

function storeSecurityAuditLog(errorLog: any): void {
  // In a real implementation, this would store in a secure audit log database
  if (securityConfig.logging.enableAuditLog) {
    console.log('SECURITY AUDIT LOG:', {
      type: 'error',
      errorId: errorLog.errorId,
      category: errorLog.secureError.category,
      severity: errorLog.secureError.severity,
      userId: errorLog.context.userId,
      ip: errorLog.context.ip,
      endpoint: errorLog.context.endpoint,
      timestamp: errorLog.timestamp
    })
  }
}

// Enhanced error creation functions
export function createValidationError(message: string, details?: any): Error {
  const error = new Error(message)
  error.name = 'VALIDATION_ERROR'
  if (details) {
    (error as any).details = details
  }
  return error
}

export function createAuthError(message: string = 'Authentication required', code?: string): Error {
  const error = new Error(message)
  error.name = code || 'AUTH_REQUIRED'
  return error
}

export function createForbiddenError(message: string = 'Access denied'): Error {
  const error = new Error(message)
  error.name = 'FORBIDDEN'
  return error
}

export function createSecurityError(message: string, type: 'CSRF_ERROR' | 'SUSPICIOUS_REQUEST' | 'SQL_INJECTION_ATTEMPT' | 'XSS_ATTEMPT'): Error {
  const error = new Error(message)
  error.name = type
  return error
}

export function createRateLimitError(retryAfter?: number): Error {
  const error = new Error('Too many requests')
  error.name = 'RATE_LIMIT'
  if (retryAfter) {
    (error as any).retryAfter = retryAfter
  }
  return error
}

// Error context builder
export function buildErrorContext(request?: NextRequest, userId?: string): ErrorContext {
  if (!request) {
    return {
      timestamp: new Date().toISOString(),
      requestId: crypto.randomUUID()
    }
  }

  return {
    userId,
    ip: getClientIP(request),
    userAgent: request.headers.get('user-agent') || undefined,
    endpoint: request.nextUrl.pathname,
    method: request.method,
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
    sessionId: request.cookies.get('session')?.value
  }
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }

  const realIP = request.headers.get('x-real-ip')
  if (realIP) {
    return realIP
  }

  const cfConnectingIP = request.headers.get('cf-connecting-ip')
  if (cfConnectingIP) {
    return cfConnectingIP
  }

  return 'unknown'
}

// Utility function for API routes
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>,
  context?: Partial<ErrorContext>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await handler(...args)
    } catch (error) {
      throw handleSecureError(error, undefined, context as ErrorContext)
    }
  }
}
