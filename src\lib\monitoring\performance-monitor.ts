/**
 * Performance Monitoring System
 * Tracks application performance metrics and provides optimization insights
 */

import { createClient } from '@/lib/supabase/server'
import { queryMonitor } from '@/lib/database/query-optimizer'
import { cacheManager } from '@/lib/cache/cache-manager'

export interface PerformanceMetrics {
  database: {
    averageQueryTime: number
    slowQueries: number
    totalQueries: number
    cacheHitRate: number
  }
  cache: {
    hits: number
    misses: number
    hitRate: number
    size: number
    entries: number
  }
  api: {
    averageResponseTime: number
    errorRate: number
    requestsPerMinute: number
  }
  system: {
    memoryUsage: number
    cpuUsage: number
    uptime: number
  }
}

export interface PerformanceAlert {
  type: 'warning' | 'critical'
  category: 'database' | 'cache' | 'api' | 'system'
  message: string
  value: number
  threshold: number
  timestamp: Date
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics
  private alerts: PerformanceAlert[] = []
  private thresholds = {
    database: {
      averageQueryTime: 500, // ms
      slowQueryCount: 10,
      cacheHitRate: 70 // %
    },
    api: {
      averageResponseTime: 1000, // ms
      errorRate: 5 // %
    },
    system: {
      memoryUsage: 80, // %
      cpuUsage: 80 // %
    }
  }

  constructor() {
    this.metrics = this.initializeMetrics()
    this.startMonitoring()
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      database: {
        averageQueryTime: 0,
        slowQueries: 0,
        totalQueries: 0,
        cacheHitRate: 0
      },
      cache: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0,
        entries: 0
      },
      api: {
        averageResponseTime: 0,
        errorRate: 0,
        requestsPerMinute: 0
      },
      system: {
        memoryUsage: 0,
        cpuUsage: 0,
        uptime: process.uptime()
      }
    }
  }

  private startMonitoring() {
    // Update metrics every 30 seconds
    setInterval(() => {
      this.updateMetrics()
    }, 30000)

    // Check for alerts every minute
    setInterval(() => {
      this.checkAlerts()
    }, 60000)
  }

  private async updateMetrics() {
    try {
      // Update database metrics
      const dbStats = queryMonitor.getStats()
      this.metrics.database = {
        averageQueryTime: dbStats.averageTime,
        slowQueries: queryMonitor.getSlowQueries().length,
        totalQueries: dbStats.totalQueries,
        cacheHitRate: dbStats.cacheHitRate
      }

      // Update cache metrics
      const cacheStats = cacheManager.getStats()
      this.metrics.cache = {
        hits: cacheStats.hits,
        misses: cacheStats.misses,
        hitRate: cacheStats.hits > 0 ? (cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100 : 0,
        size: cacheStats.size,
        entries: cacheStats.entries
      }

      // Update system metrics
      this.metrics.system = {
        memoryUsage: this.getMemoryUsage(),
        cpuUsage: await this.getCPUUsage(),
        uptime: process.uptime()
      }

    } catch (error) {
      console.error('Error updating performance metrics:', error)
    }
  }

  private checkAlerts() {
    const now = new Date()
    
    // Database alerts
    if (this.metrics.database.averageQueryTime > this.thresholds.database.averageQueryTime) {
      this.addAlert({
        type: 'warning',
        category: 'database',
        message: 'High average query time detected',
        value: this.metrics.database.averageQueryTime,
        threshold: this.thresholds.database.averageQueryTime,
        timestamp: now
      })
    }

    if (this.metrics.database.slowQueries > this.thresholds.database.slowQueryCount) {
      this.addAlert({
        type: 'critical',
        category: 'database',
        message: 'Too many slow queries detected',
        value: this.metrics.database.slowQueries,
        threshold: this.thresholds.database.slowQueryCount,
        timestamp: now
      })
    }

    if (this.metrics.database.cacheHitRate < this.thresholds.database.cacheHitRate) {
      this.addAlert({
        type: 'warning',
        category: 'database',
        message: 'Low cache hit rate',
        value: this.metrics.database.cacheHitRate,
        threshold: this.thresholds.database.cacheHitRate,
        timestamp: now
      })
    }

    // System alerts
    if (this.metrics.system.memoryUsage > this.thresholds.system.memoryUsage) {
      this.addAlert({
        type: 'warning',
        category: 'system',
        message: 'High memory usage',
        value: this.metrics.system.memoryUsage,
        threshold: this.thresholds.system.memoryUsage,
        timestamp: now
      })
    }
  }

  private addAlert(alert: PerformanceAlert) {
    // Avoid duplicate alerts within 5 minutes
    const recentAlert = this.alerts.find(a => 
      a.category === alert.category && 
      a.message === alert.message &&
      (Date.now() - a.timestamp.getTime()) < 300000 // 5 minutes
    )

    if (!recentAlert) {
      this.alerts.push(alert)
      
      // Keep only last 100 alerts
      if (this.alerts.length > 100) {
        this.alerts = this.alerts.slice(-100)
      }

      // Log critical alerts
      if (alert.type === 'critical') {
        console.error('CRITICAL PERFORMANCE ALERT:', alert)
      } else {
        console.warn('Performance Alert:', alert)
      }
    }
  }

  private getMemoryUsage(): number {
    const usage = process.memoryUsage()
    const totalMemory = usage.heapTotal + usage.external
    const usedMemory = usage.heapUsed
    return (usedMemory / totalMemory) * 100
  }

  private async getCPUUsage(): Promise<number> {
    // Simple CPU usage estimation based on event loop delay
    const start = process.hrtime.bigint()
    await new Promise(resolve => setImmediate(resolve))
    const end = process.hrtime.bigint()
    const delay = Number(end - start) / 1000000 // Convert to milliseconds
    
    // Rough estimation: delay > 10ms indicates high CPU usage
    return Math.min((delay / 10) * 100, 100)
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  public getAlerts(category?: string): PerformanceAlert[] {
    if (category) {
      return this.alerts.filter(alert => alert.category === category)
    }
    return [...this.alerts]
  }

  public getRecentAlerts(minutes = 60): PerformanceAlert[] {
    const cutoff = Date.now() - (minutes * 60 * 1000)
    return this.alerts.filter(alert => alert.timestamp.getTime() > cutoff)
  }

  public async getDatabaseInsights(): Promise<{
    slowQueries: any[]
    indexUsage: any[]
    tableSizes: any[]
  }> {
    try {
      const supabase = await createClient()
      
      const [slowQueries, indexUsage, tableSizes] = await Promise.all([
        supabase.from('slow_queries').select('*').limit(10),
        supabase.from('index_usage').select('*').order('idx_scan', { ascending: false }).limit(20),
        supabase.from('table_sizes').select('*').limit(10)
      ])

      return {
        slowQueries: slowQueries.data || [],
        indexUsage: indexUsage.data || [],
        tableSizes: tableSizes.data || []
      }
    } catch (error) {
      console.error('Error fetching database insights:', error)
      return {
        slowQueries: [],
        indexUsage: [],
        tableSizes: []
      }
    }
  }

  public generateOptimizationReport(): {
    summary: string
    recommendations: string[]
    metrics: PerformanceMetrics
    alerts: PerformanceAlert[]
  } {
    const recommendations: string[] = []
    
    // Database recommendations
    if (this.metrics.database.averageQueryTime > 200) {
      recommendations.push('Consider optimizing slow database queries')
    }
    
    if (this.metrics.database.cacheHitRate < 80) {
      recommendations.push('Increase cache TTL for frequently accessed data')
    }
    
    // Cache recommendations
    if (this.metrics.cache.hitRate < 60) {
      recommendations.push('Review caching strategy for better hit rates')
    }
    
    // System recommendations
    if (this.metrics.system.memoryUsage > 70) {
      recommendations.push('Monitor memory usage and consider optimization')
    }

    const criticalAlerts = this.alerts.filter(a => a.type === 'critical').length
    const warningAlerts = this.alerts.filter(a => a.type === 'warning').length
    
    const summary = `Performance Summary: ${criticalAlerts} critical alerts, ${warningAlerts} warnings. ` +
      `Average query time: ${this.metrics.database.averageQueryTime.toFixed(2)}ms, ` +
      `Cache hit rate: ${this.metrics.cache.hitRate.toFixed(1)}%`

    return {
      summary,
      recommendations,
      metrics: this.metrics,
      alerts: this.getRecentAlerts(60)
    }
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Utility functions for performance tracking
export function trackApiCall<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const start = Date.now()
  
  return fn().finally(() => {
    const duration = Date.now() - start
    console.log(`API Operation: ${operation} took ${duration}ms`)
    
    if (duration > 1000) {
      console.warn(`Slow API operation detected: ${operation} (${duration}ms)`)
    }
  })
}

export function measureQueryPerformance<T>(
  queryName: string,
  queryFn: () => Promise<T>
): Promise<T> {
  const start = Date.now()
  
  return queryFn().finally(() => {
    const duration = Date.now() - start
    console.log(`Database Query: ${queryName} took ${duration}ms`)
  })
}
